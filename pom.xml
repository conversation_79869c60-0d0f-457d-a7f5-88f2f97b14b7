<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.sumo</groupId>
  <artifactId>sm-offer-service</artifactId>
  <version>1.0.0-SM</version>
  
  <packaging>pom</packaging>
  <name>OfferService</name>

  <modules>
    <module>offer-api</module>
    <module>offer-service</module>
    <module>offer-pipeline</module>
    <module>external-service-client</module>
    <module>offer-common</module>
    <module>offer-internal-models</module>
    <module>offer-orchestrator</module>
    <module>lender-blackbox</module>
    <module>application-service</module>
    <module>experiment-manager</module>
    <module>offer-evaluator-v2</module>
    <module>cohort-finder-v2</module>
    <module>coverage-reports</module>
  </modules>
  
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <dropwizard.version>4.0.7</dropwizard.version>
    <pandora.version>1.4.59-SM</pandora.version>
    <pinaka.version>3.4.23-SM</pinaka.version>
  </properties>

  <repositories>
    <repository>
      <id>fk-art-snapshot</id>
      <name>Flipkart-Artifactory</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </repository>
    <repository>
      <id>fk-art-release</id>
      <name>Flipkart-Artifactory</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </repository>
  </repositories>

  <distributionManagement>
    <snapshotRepository>
      <id>fk-art-snapshot</id>
      <name>libs-snapshot</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </snapshotRepository>
    <repository>
      <id>fk-art-release</id>
      <name>libs-rel</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </repository>
  </distributionManagement>

  <pluginRepositories>
    <pluginRepository>
      <id>fk-art-snapshot</id>
      <name>libs-snapshot</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </pluginRepository>
    <pluginRepository>
      <id>fk-art-release</id>
      <name>libs-rel</name>
      <url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url>
    </pluginRepository>
  </pluginRepositories>
  
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>dropwizard-dependencies</artifactId>
        <version>${dropwizard.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>31.1-jre</version>
      </dependency>

      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.7.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey</groupId>
        <artifactId>jersey-bom</artifactId>
        <version>3.1.6</version>
        <type>pom</type>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-bom</artifactId>
        <version>4.11.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-base</artifactId>
        <version>2.16.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>jakarta.validation</groupId>
        <artifactId>jakarta.validation-api</artifactId>
        <version>3.1.0-M1</version>
      </dependency>
      <dependency>
        <groupId>jakarta.persistence</groupId>
        <artifactId>jakarta.persistence-api</artifactId>
        <version>3.2.0-M1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  
  <dependencies>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.12</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate.orm</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>6.4.4.Final</version>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <version>8.3.0</version>
    </dependency>
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
      <version>3.1.0-M1</version>
    </dependency>
    <dependency>
      <groupId>ru.vyarus</groupId>
      <artifactId>dropwizard-guicey</artifactId>
      <version>7.1.3</version>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>7.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.sumo.dataplatform</groupId>
      <artifactId>event-publisher</artifactId>
      <version>1.5.19</version>
    </dependency>
    <dependency>
      <groupId>com.sumo.dataplatform</groupId>
      <artifactId>schema-registry</artifactId>
      <version>1.5.19</version>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>3.5.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.12.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.5.0</version>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>0.8.11</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <release>17</release>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <workingDirectory>${project.build.directory}</workingDirectory>
          <systemPropertyVariables>
            <http.workingDirectory>${project.build.directory}</http.workingDirectory>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>prepare-agent</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
          <execution>
            <id>post-unit-test</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
            <configuration>
              <!-- Sets the path to the file which contains the execution data. -->
              <dataFile>${project.build.directory}/jacoco.exec</dataFile>
              <!-- Sets the output directory for the code coverage report. -->
              <outputDirectory>${project.reporting.outputDirectory}/jacoco-ut</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
