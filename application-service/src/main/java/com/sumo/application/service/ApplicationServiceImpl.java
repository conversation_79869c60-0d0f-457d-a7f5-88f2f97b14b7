package com.sumo.application.service;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.google.inject.Inject;
import com.sumo.offer.common.metric.MetricsConstant;
import com.sumo.offer.common.metric.OfferServiceMetricRegistry;
import com.sumo.offer.internal.models.ApplicationService.WorkflowApplication;
import com.sumo.offer.internal.models.ApplicationService.WorkflowState;
import com.sumo.offer.internal.models.ApplicationService.WorkflowStateFetchRequest;
import com.sumo.offer.internal.models.ApplicationService.WorkflowStateFetchResponse;
import jakarta.inject.Named;
import jakarta.ws.rs.client.*;
import lombok.extern.slf4j.Slf4j;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;


import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.sumo.offer.common.metric.MetricsConstant.LENDER_BLACKBOX_METRIC;
import static com.sumo.offer.common.metric.MetricsConstant.WINTERFELL_GET_APP_METRIC;

@Slf4j
public class ApplicationServiceImpl implements ApplicationService{

    private final ObjectMapper mapper = new ObjectMapper();
    private final int timeoutInSeconds = 1;
    private final String winterfellClientUrl;
    private final MetricRegistry metricsRegistry;
    private final Client client = ClientBuilder.newBuilder()
            .connectTimeout(timeoutInSeconds, TimeUnit.SECONDS)
            .readTimeout(timeoutInSeconds, TimeUnit.SECONDS)
            .build();
    @Inject
    public ApplicationServiceImpl(@Named("winterfellClientUrl") String winterfellClientUrl, MetricRegistry metricsRegistry) {
        this.winterfellClientUrl = winterfellClientUrl;
        this.metricsRegistry = OfferServiceMetricRegistry.getMetricRegistry();
        mapper.setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS);
        mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true);
    }

    @Override
    public List<WorkflowApplication> getDiscardedApplications(MerchantUser merchantUser) {
        WorkflowStateFetchRequest request = new WorkflowStateFetchRequest(
                merchantUser.getMerchantUserId(), merchantUser.getSmUserId(), null, List.of(WorkflowState.DISCARDED)
        );
        return getApplications(request);
    }

    private List<WorkflowApplication> getApplications(WorkflowStateFetchRequest requestBody) {
        Response response = null;
        try (Timer.Context timer = metricsRegistry.timer(MetricRegistry.name(ApplicationServiceImpl.class,
                WINTERFELL_GET_APP_METRIC)).time()){
            WebTarget target = client.target(winterfellClientUrl + "/1/application/workflow-state-fetch");
            Invocation.Builder request = target.request(MediaType.APPLICATION_JSON)
                    .header("X-Tenant-Id", "CALM")
                    .header("X-Client-ID", "CALM")
                    .header("X-Request-ID", "test")
                    .header("Content-Type", "application/json")
                    .header("X-Trace-ID", "test");
            response = request.post(Entity.entity(mapper.writeValueAsString(requestBody), MediaType.APPLICATION_JSON));
            if (response != null && response.getStatus() == 200) {
                String jsonResponse = response.readEntity(String.class);
                WorkflowStateFetchResponse workflowStateFetchResponse = mapper.readValue(jsonResponse, WorkflowStateFetchResponse.class);
                return workflowStateFetchResponse.getApplicationsList();
            } else {
                log.error(String.format("ApplicationServiceImpl:: unable to get response from winterfell %s", response != null ? response.readEntity(String.class) : "no response"));
                metricsRegistry.meter(MetricRegistry.name(ApplicationServiceImpl.class, MetricsConstant.WINTERFELL_GET_APP_EXCEPTION)).mark();
            }
        } catch (Exception e) {
            log.error(String.format("ApplicationServiceImpl:: Failed to fetch applications from winterfell client due to %s", e.getMessage()));
            metricsRegistry.meter(MetricRegistry.name(ApplicationServiceImpl.class, MetricsConstant.WINTERFELL_GET_APP_EXCEPTION)).mark();
            return null;
        }finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

}