CREATE TABLE user_cohort
(
    id BIGINT AUTO_INCREMENT PRIMARY KEY ,
    group_id      BIGINT,
    rules         TEXT,
    created_by    <PERSON><PERSON><PERSON><PERSON>(255),
    created_at_ms BIGINT DEFAULT NULL,
    name          <PERSON><PERSON><PERSON><PERSON>(255),
    INDEX         idx_group_id (group_id)
);

DELIMITER //

CREATE TRIGGER before_insert_user_cohort
    BEFORE INSERT ON user_cohort
    FOR EACH ROW
BEGIN
    IF NEW.created_at_ms IS NULL THEN
        SET NEW.created_at_ms = UNIX_TIMESTAMP() * 1000;
END IF;
END //

DELIMITER ;


CREATE TABLE user_cohort_group (
                                   id BIGINT AUTO_INCREMENT PRIMARY KEY,
                                   version DOUBLE,
                                   created_by <PERSON><PERSON><PERSON><PERSON>(255),
                                   created_at_ms BIGINT DEFAULT NULL,
                                   description VARCHAR(1279),
                                   INDEX idx_version (version)
);

DELIMITER //

CREATE TRIGGER before_insert_user_cohort_group
    BEFORE INSERT ON user_cohort_group
    FOR EACH ROW
BEGIN
    IF NEW.created_at_ms IS NULL THEN
        SET NEW.created_at_ms = UNIX_TIMESTAMP() * 1000;
END IF;
END //

DELIMITER ;

ALTER TABLE user_cohort
    ADD CONSTRAINT fk_user_cohort_group_id
        FOREIGN KEY (group_id)
            REFERENCES user_cohort_group(id);

