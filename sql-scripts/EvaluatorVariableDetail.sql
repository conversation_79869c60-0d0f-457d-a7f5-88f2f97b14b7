create table evaluator_variable_detail(id BIGINT AUTO_INCREMENT PRIMARY KEY,
                                       variable VARCHAR(255) NOT NULL,
                                       cohort_id BIGINT NOT NULL,
                                       offer_types VARCHAR(1275) NOT NULL,
                                       value DOUBLE,
                                       lender <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
                                       version DOU<PERSON><PERSON>,
                                       created_at_ms BIGINT,
                                       created_by VA<PERSON><PERSON>R(255));

DELIMITER //

CREATE TRIGGER set_created_at_before_insert
    BEFORE INSERT ON evaluator_variable_detail
    FOR EACH ROW
BEGIN
    SET NEW.created_at_ms = UNIX_TIMESTAMP() * 1000;
END//

DELIMITER ;