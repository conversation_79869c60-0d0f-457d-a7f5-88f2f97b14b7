namespace: sm-offer-service-prod

fcp:
  team: "supermoney-engg"
  appId: "sm-offer-service-prod"
  mail: "supermoney"
  service: "sm-offer-service-prod"

mtl:
  cosmos:
    config: cosmos-config
  asterix: disabled
  logsvc:
    enabled: false
    envName: calvin
    sourceLoc: rsyslog.conf

pod:
  replicaCount: 3

hostpopulator:
  state: enabled
  cpu: 0.2
  nameservers:
    - dns.fkinternal.com

image:
  service:
    tag: "1741119-2632-flow-487f683961d877d96ebc45cd08c49e306c237272-13-Jan-23_12-52-53"
    repository: supermoney-engg/sm-offer-service-preprod
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-config
      - app-log
      - cosmos-config
      - cfg-svc-metadata
      - relayer-config
      - host-populator-logs
      - host-d
      - host-populator-config
      - sumo-observability-volume
    livenessCheck:
      initialDelay: 85
      checkInterval: 15
      method: tcp
      port: 9091
    readinessCheck:
      initialDelay: 65
      checkInterval: 15
      method: http
      port: 9091
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 9090
      - name: admin
        containerPort: 9091
      - name: jmx
        containerPort: 9311
    maxCpuLimit: 2
    maxCpuAsk: 2
    maxMemoryLimit: 6Gi
    maxMemoryAsk: 6Gi
    envVars:
      - name: SUMO_OBSERVABILITY_ENABLED
        source: value
        value: "TRUE"
  fluentbit:
    registry: edge.fkinternal.com/docker-external
    repository: grafana/fluent-bit-plugin-loki
    tag: main-e2ed1c0
    maxCpuLimit: 0.25
    maxCpuAsk: 0.25
    maxMemoryLimit: 500Mi
    maxMemoryAsk: 500Mi
    volumes:
      - fluent-bit-config-volume
      - app-log

volumes:
  - name: app-config
    type: configMap
    value: app-config
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata
    type: configMap
    value: cfg-svc-metadata
    mountPath: /etc/default/
  - name: relayer-config
    type: configMap
    value: relayer-config
    mountPath: /etc/fk-sc-mq/
  - name: host-populator-logs
    type: emptyDir
    value: { }
    mountPath: /var/log/hosts-populator/
  - name: host-d
    type: emptyDir
    value: { }
    mountPath: /etc/hosts.d/
  - name: fluent-bit-config-volume
    type: configMap
    value: fluent-bit-config-map
    mountPath: /fluent-bit/etc/
  - name: sumo-observability-volume
    type: configMap
    value: sumo-observability-config
    mountPath: /etc/sumo-observability/

serviceAccount:
  name: sm-offer-service-user
  annotations:
    gcp_service_account: "<EMAIL>"

configMaps:
  envName: gcp-prod
  configs:
    - name: app-config
      source : dir
      sourceLoc : app
    - name: relayer-config
      source: dir
      sourceLoc: relayer
    - name: cosmos-config
      source: dir
      sourceLoc: cosmos
    - name: host-populator-config
      source: dir
      sourceLoc: host-populator
    - name: fluent-bit-config-map
      source: dir
      sourceLoc: fluent-bit
    - name: cfg-svc-metadata
      source: data
      data:
        cfg-api: |
          host=api.aso1.cfgsvc-prod.fkcloud.in
          port=80
        fk-env: |
          prod
    - name: sumo-observability-config
      source: dir
      sourceLoc: sumo-observability

services:
  - name: offer-service
    type: LoadBalancer
    ports:
      - port: 9090 # useless but not to be changed
        targetPort: 9090
        protocol: TCP
        name: sm-offer-service-prod
    LoadBalancer:
      vip: sm-offer-service-prod-elb
      healthCheckPort: 9091
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-offer-service-prod-elb
      backendPort: 9090
      frontendPort: 80
      mode: http
