server:
  applicationContextPath: /offers
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
    # Note:
    # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
    # 2. If we specify a logFormat, note that the format specifiers for access logs are different
    # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
    appenders:
      - type: file
        currentLogFilename: /var/log/offer-service/access.log
        threshold: ALL
        archive: true
        archivedLogFilenamePattern: /var/log/offer-service/access-%d{yyyy-MM-dd-HH}.log.gz
        archivedFileCount: 24
logging:
  level: INFO
  loggers:
    com.sumo: DEBUG
  appenders:
    - type: file
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message %replace(%exception){'\n',' | '} %n %nopex"
      currentLogFilename: /var/log/offer-service/offer-service.log
      archivedLogFilenamePattern: /var/log/offer-service/offer-service-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
    - type: file
      threshold: ERROR
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message %replace(%exception){'\n',' | '} %n %nopex"
      currentLogFilename: /var/log/offer-service/offer-service-error.log
      archivedLogFilenamePattern: /var/log/offer-service/offer-service-error-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
    - type: console
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message%n"
      target: stdout


leaderDatabaseConfig:
  url: *******************************************
  user: sm_admin
  password: F7yd#J_*;K{[0HV-
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQLDialect
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 2s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 50
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection


dynamicBucketName: sm-offer-service-prod

pandoraClientConfig:
  url: http://pandora-service.sm-pandora-prod.fkcloud.in/pandora
  client: pinaka

internalBlackboxFeatureUrl: http://sm-internal-blackbox.sm-lender-blackbox-service-prod.fkcloud.in/experian-report/feature

internalBlackboxUrl: http://sm-internal-blackbox.sm-lender-blackbox-service-prod.fkcloud.in/score

internalModelUrl: http://internal-model.sm-lender-blackbox-service-prod.fkcloud.in

winterfellClientUrl: http://winterfell-service.sm-winterfell-prod.fkcloud.in/fintech-winterfell

pandoraClientUrl: http://pandora-service.sm-pandora-prod.fkcloud.in/pandora

fibeBlackboxAuthorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6IlN1cGVyTW9uZXkxMzE4OTUwMiIsInBhc3N3b3JkIjoic21RNjdPZjltSjZWIiwiZXhwIjoyNjYxOTI2MzAxfQ.Nt0msj7usX6QbhTgqE5i5VaQYehZDoGakXIBiP4MqDE

internalBlackboxAuthorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6IlN1cGVyTW9uZXkxMzE4OTUwMiIsInBhc3N3b3JkIjoic21RNjdPZjltSjZWIiwiZXhwIjoyNjYxOTI2MzAxfQ.Nt0msj7usX6QbhTgqE5i5VaQYehZDoGakXIBiP4MqDE


moneyviewBlackboxUrl: http://moneyview.sm-lender-blackbox-service-prod.fkcloud.in/blackbox/api/score/bb/getOfferV2

whitelistBatchSize: 1000

environment: "PROD"

finnableBlackboxUrl: http://finnable-v2.sm-lender-blackbox-service-prod.fkcloud.in/finnableoffer

ringBlackboxUrl: http://ring.sm-lender-blackbox-service-prod.fkcloud.in/api/v1/fetch-offers

networkClientTimeoutInMS: 2500

subscriberConfig:
  projectId: sm-personal-loans
  subscriptionId: sub-blackbox-offers
  messageReceiverCount: 1
  consumerThreadCount: 5

paOfferEventSubscriberConfig:
  projectId: sm-data-platform
  subscriptionId: "OfferService.offer_service.preapproved_offers_discoverypa_compute"
  messageReceiverCount: 5
  consumerThreadCount: 10

boomerangClientConfig:
  host: http://boomerang-service.sumo-boomerang-prod.fkcloud.in/boomerang

redisConfiguration:
  primaryHost: ************
  readHost: ************
  namespace: "supermoney"
  groupName: "sm-offer-cache"

revelioRedisConfiguration:
  primaryHost: ***********:6379
  readHost: ***********:6379
  namespace: "supermoney"
  groupName: "pl-revelio-svc"

kavachClientConfiguration:
  endpoint: "http://kavach-service-prod.kavach-prod.fkcloud.in"
  clientId: "sm-offer-service"
