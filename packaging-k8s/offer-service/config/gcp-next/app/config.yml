server:
  applicationContextPath: /offers
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
    # Note:
    # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
    # 2. If we specify a logFormat, note that the format specifiers for access logs are different
    # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
    appenders:
      - type: file
        currentLogFilename: /var/log/offer-service/access.log
        threshold: ALL
        archive: true
        archivedLogFilenamePattern: /var/log/offer-service/access-%d{yyyy-MM-dd-HH}.log.gz
        archivedFileCount: 24
logging:
  level: INFO
  loggers:
    com.sumo: DEBUG
  appenders:
    - type: file
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/offer-service/offer-service.log
      archivedLogFilenamePattern: /var/log/offer-service/offer-service-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
    - type: file
      threshold: ERROR
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      currentLogFilename: /var/log/offer-service/offer-service-error.log
      archivedLogFilenamePattern: /var/log/offer-service/offer-service-error-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
    - type: console
      threshold: ALL
      logFormat: "%-5level [%date] [%thread] [%X{X-Request-ID}] [%cyan(%logger{0})]: %message%n"
      target: stdout

leaderDatabaseConfig:
  url: *************************************************************************************
  user: sm_admin
  password: G3XWFs29c8
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQLDialect
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 2s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 50
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection

bigFootConfiguration:
  url: http://10.83.34.172:28223

dynamicBucketName: sm-offer-service-next

pandoraClientConfig:
  url: http://pandora-service-next.sm-pandora-prod.fkcloud.in/pandora
  client: pinaka

bigfootEntityConfig:
  borrowerConfig:
    name: fintech_borrowers
    schemaVersion: 1.7
  loanApplicationConfig:
    name: fintech_loan_application
    schemaVersion: 4.2
  pLApplicationConfig:
    name: sm_loan_application
    schemaVersion: 1.2
  subApplicationConfig:
    name: fintech_loan_subapplication
    schemaVersion: 3.0
  whitelistConfig:
    name: fintech_whitelist
    schemaVersion: 2.0
  sourceAttributionConfig:
    name: fintech_source_attribution
    schemaVersion: 1.0
  ebcApplicationConfig:
    name: fintech_ebc_borrower
    schemaVersion : 1.0
  pincodeConfig:
    name: fintech_non_serviceable_pincode
    schemaVersion: 1.0
  tnsScoreConfig:
    name: SMPLUserTnsScore
    schemaVersion: 1.1
  revalidationApplicationConfig:
    name: fintech_revalidation_application
    schemaVersion: 2.1
  offerOrchestrationConfig:
    name: sm_pl_offer_orchestration_event
    schemaVersion: 3.1
  blackboxEventConfig:
    name: sm_pl_lender_blackbox_event
    schemaVersion: 1.0

internalBlackboxFeatureUrl: http://internal-blackbox.sm-lender-blackbox-playground.fkcloud.in/experian-report/feature

internalBlackboxUrl: http://internal-blackbox.sm-lender-blackbox-playground.fkcloud.in/score

winterfellClientUrl: http://winterfell-service-next.sm-winterfell-prod.fkcloud.in/fintech-winterfell

internalBlackboxAuthorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6IlN1cGVyTW9uZXkxMzE4OTUwMiIsInBhc3N3b3JkIjoic21RNjdPZjltSjZWIiwiZXhwIjoyNjYxOTI2MzAxfQ.Nt0msj7usX6QbhTgqE5i5VaQYehZDoGakXIBiP4MqDE

moneyviewBlackboxUrl: http://moneyview.sm-lender-blackbox-service-prod.fkcloud.in/blackbox/api/score/bb/getOfferV2

environment: "NEXT"

finnableBlackboxUrl: http://finnable.sm-lender-blackbox-playground.fkcloud.in/finnableoffer

networkClientTimeoutInMS: 2000

pandoraClientUrl: http://pandora-service-next.sm-pandora-prod.fkcloud.in/pandora

subscriberConfig:
  projectId: sm-personal-loans
  subscriptionId: sub-blackbox-offers
  messageReceiverCount: 1
  consumerThreadCount: 5

boomerangClientConfig:
  host: http://boomerang-service.sumo-boomerang-prod.fkcloud.in/boomerang