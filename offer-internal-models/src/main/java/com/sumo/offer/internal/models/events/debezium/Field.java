package com.sumo.offer.internal.models.events.debezium;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;

import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Field {
    private String type;
    private Field[] fields;
    private boolean optional;
    private String name;
    private String field;
    private String version;
    private Map<String, String> parameters;
    private String defaultValue;
}
