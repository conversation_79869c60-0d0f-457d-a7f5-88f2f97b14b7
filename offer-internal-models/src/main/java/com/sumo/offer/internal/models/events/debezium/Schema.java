package com.sumo.offer.internal.models.events.debezium;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Schema {
    private String type;
    private Field[] fields;
    private boolean optional;
    private String name;
}
