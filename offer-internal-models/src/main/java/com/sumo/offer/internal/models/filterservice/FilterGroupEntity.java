package com.sumo.offer.internal.models.filterservice;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.sumo.offer.internal.models.OrchestratorProductType;
import lombok.Data;

import jakarta.persistence.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

@Entity
@Data
@Table(name = "filter_group")
public class FilterGroupEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "lender")
    private Lender lender;

    @Column(name = "version")
    private Double version;

    @Column(name = "filters")
    private String filters;

    @Column(name = "created_at_ms")
    private Long createdAtMS;

    @Column(name = "updated_at_ms")
    private Long updatedAtMS;

    @Enumerated(EnumType.STRING)
    @Column(name = "product_type")
    private OrchestratorProductType productType;

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        this.createdAtMS = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
        this.updatedAtMS = this.createdAtMS;
    }

    @PreUpdate
    protected void onUpdate() {
        Instant now = Instant.now();
        this.updatedAtMS = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
    }

    public LocalDateTime getCreatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(createdAtMS), ZoneId.of("Asia/Kolkata"));
    }

    public LocalDateTime getUpdatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(createdAtMS), ZoneId.of("Asia/Kolkata"));
    }

    public List<Filter> getFilters() {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(filters, new TypeReference<List<Filter>>() {});
        } catch (Exception e) {
            throw new RuntimeException(String.format("FilterGroup::Unable to parse filters for id %s", this.id));
        }
    }

    public void setFilters(List<Filter> filterList) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            this.filters = mapper.writeValueAsString(filterList);
        } catch (Exception e) {
            throw new RuntimeException(String.format("FilterGroup::Unable to parse filters for id %s", this.id));
        }
    }


}
