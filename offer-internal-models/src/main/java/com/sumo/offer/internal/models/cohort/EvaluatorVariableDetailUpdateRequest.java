package com.sumo.offer.internal.models.cohort;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.OfferType;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.sumo.offer.internal.models.LenderOfferType;
import com.sumo.offer.internal.models.evaluator.EvaluatorVariableName;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EvaluatorVariableDetailUpdateRequest {

    @JsonProperty("cohort_id")
    private String cohortId;

    @JsonProperty("variable")
    @Enumerated(EnumType.STRING)
    private EvaluatorVariableName variable;

    @JsonProperty("offer_types")
    private List<LenderOfferType> offerTypes;

    @JsonProperty("value")
    private Double value;

    @JsonProperty("lender")
    @Enumerated(EnumType.STRING)
    private Lender lender;

}

