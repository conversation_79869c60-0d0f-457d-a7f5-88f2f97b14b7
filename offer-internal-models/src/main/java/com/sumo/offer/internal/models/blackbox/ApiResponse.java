package com.sumo.offer.internal.models.blackbox;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ApiResponse {
    private String body;
    private Integer status;
    private String error;
    private Long latency;

    public ApiResponse(String jsonResponse, Integer status, String error, Long latency) {
        this.body = jsonResponse;
        this.status = status;
        this.error = error;
        this.latency = latency;
    }
}
