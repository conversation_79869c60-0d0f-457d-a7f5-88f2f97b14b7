package com.sumo.offer.internal.models.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.sumo.offer.internal.models.LenderOfferEntity;
import com.sumo.offer.internal.models.LenderOfferType;
import com.sumo.offer.internal.models.entity.compositeKeys.PreapprovedUserId;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 23/07/24
 */
@Entity
@IdClass(PreapprovedUserId.class)
@Table(name = "preapproved_offers")
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Slf4j
public class PreapprovedOffersEntity {

    @Id
    @Column(name = "sm_user_id")
    @JsonProperty("sm_user_id")
    private String smUserId;

    @Id
    @JsonProperty("offer_id")
    @Column(name = "offer_id")
    private String offerId;

    @Column(name = "whitelist_id")
    @JsonProperty("whitelist_id")
    private Long whitelistId;

    @Column(name = "roi")
    private Double roi;

    @Column(name = "tenure")
    private Integer tenure;

    @Column(name = "max_sanctioned_amount")
    @JsonProperty("max_sanctioned_amount")
    private Long max_sanctioned_amount;

    @Column(name = "employment_type")
    @JsonProperty("employment_type")
    private String employmentType;

    @Column(name = "expiry_date")
    @JsonProperty("expiry_date")
    private Timestamp expiryDate;

    @CreationTimestamp
    @Column(name = "created_at")
    @JsonProperty("created_at")
    private Timestamp createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    @JsonProperty("updated_at")
    private Timestamp updatedAt;

    @Column(name = "enabled")
    private Boolean enabled;

    @Column(name = "metadata")
    private String metadata;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        PreapprovedOffersEntity that = (PreapprovedOffersEntity) o;
        return smUserId != null && Objects.equals(smUserId, that.smUserId)
                && offerId != null && Objects.equals(offerId, that.offerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(smUserId, offerId);
    }

    public LenderOfferEntity mapToLenderOfferEntity(WhitelistEntity entity) {
        try {
            LenderOfferEntity lenderOfferEntity = new LenderOfferEntity();
            lenderOfferEntity.setLender(Lender.valueOf(entity.getLender()));
            lenderOfferEntity.setUserId(this.smUserId);
            lenderOfferEntity.setOfferType(LenderOfferType.PRE_APPROVED);
            lenderOfferEntity.setId(this.smUserId+this.offerId);
            Map<String,Object> metadata = new HashMap<>();
            metadata.put("offer_id", this.offerId);
            lenderOfferEntity.setMetadata(ObjectMapperUtil.get().writeValueAsString(metadata));
            lenderOfferEntity.setAmount(this.max_sanctioned_amount);
            lenderOfferEntity.setRoi(this.roi);
            lenderOfferEntity.setLenderDetails(new HashMap<>());
            return lenderOfferEntity;
        } catch (JsonProcessingException e) {
            log.error("Error mapping preapproved offer to Lender Offer Entity : " + e.getMessage(), e);
        }
        return null;
    }
}
