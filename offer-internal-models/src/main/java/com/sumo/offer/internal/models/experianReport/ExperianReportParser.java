package com.sumo.offer.internal.models.experianReport;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;

@Slf4j
public class ExperianReportParser {

    public static ExperianReport parse(String report) {

        ExperianReport experianReport = null;
        try {
            XmlMapper xmlMapper = new XmlMapper();
            experianReport = xmlMapper.readValue(report, ExperianReport.class);
        }catch (Exception e){
            log.error("Unable to parse report due to the following " + e.getMessage());
        }
        return experianReport;
    }
}
