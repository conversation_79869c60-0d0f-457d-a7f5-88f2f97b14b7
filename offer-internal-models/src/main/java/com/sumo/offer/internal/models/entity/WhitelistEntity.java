package com.sumo.offer.internal.models.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 23/07/24
 */
@Entity
@Table(name = "whitelist")
@Data
public class WhitelistEntity {
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "whitelist_id")
    private Long whitelistId;

    @Column(name = "whitelist_name")
    private String whitelistName;

    @ManyToOne(cascade = {CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id", nullable = false)
    private MerchantEntityNew merchant;

    @Column(name = "enabled")
    private Boolean enabled;

    @Column(name = "lender")
    private String lender;

    @CreationTimestamp
    @Column(name = "created_at")
    private Timestamp createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private Timestamp updatedAt;

    @Column(name = "product_type")
    private String productType;
}
