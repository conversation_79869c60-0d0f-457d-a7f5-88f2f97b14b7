package com.sumo.offer.internal.models.events;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sumo.offer.internal.models.entity.PreapprovedOffersEntity;
import com.sumo.offer.internal.models.events.debezium.Payload;
import com.sumo.offer.internal.models.events.debezium.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaOfferCdcEvent {
    private Schema schema;
    private Payload<PreapprovedOffersEventPayload> payload;
}