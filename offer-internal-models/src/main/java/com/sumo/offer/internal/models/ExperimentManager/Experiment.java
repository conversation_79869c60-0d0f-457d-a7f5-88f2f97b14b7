package com.sumo.offer.internal.models.ExperimentManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

@Entity
@Table(name = "experiment")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Slf4j
public class Experiment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false, nullable = false)
    private Long id;

    @Column(name = "subject_type")
    @Enumerated(EnumType.STRING)
    private ExperimentSubjectType subjectType;

    @Column(name = "experiment_type")
    @Enumerated(EnumType.STRING)
    private ExperimentType experimentType;

    @Column(name = "experiment_subject_configuration")
    private String experimentSubjectConfigurations;

    @Column(name = "state")
    @Enumerated(EnumType.STRING)
    private ExperimentState state;

    @Column(name = "created_at_ms")
    private Long createdAtMs;

    @Column(name = "updated_at_ms")
    private Long updatedAtMs;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "weight")
    private Integer weight;

    @Transient
    private List<ExperimentSubjectConfiguration> experimentSubjectConfigurationParsed;

    public List<ExperimentSubjectConfiguration> getExperimentSubjectConfigurations() {
        if (experimentSubjectConfigurationParsed != null) return experimentSubjectConfigurationParsed;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            experimentSubjectConfigurationParsed = objectMapper.readValue(
                    experimentSubjectConfigurations, new TypeReference<List<ExperimentSubjectConfiguration>>() {
                    });
        } catch (IOException e) {
            log.error(String.format("Failed to parse experimentSubjectConfigurationParsed due to %s ", e.getMessage()));
        }
        return experimentSubjectConfigurationParsed;
    }

    public void setExperimentSubjectConfigurationList(List<ExperimentSubjectConfiguration> experimentSubjectConfigurationList) {
        experimentSubjectConfigurationParsed = experimentSubjectConfigurationList;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            this.experimentSubjectConfigurations = objectMapper.writeValueAsString(experimentSubjectConfigurationList);
        } catch (IOException e) {

        }
    }

}
