package com.sumo.offer.internal.models.ApplicationService;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowApplication {
    @JsonProperty("application_id")
    private String applicationId;

    @JsonProperty("workflow_state")
    private String workflowState;

    @JsonProperty("application_state")
    private String applicationState;

    @JsonProperty("application_type")
    private String applicationType;

    @JsonProperty("created_at")
    private long createdAt;

    @JsonProperty("updated_at")
    private long updatedAt;
}