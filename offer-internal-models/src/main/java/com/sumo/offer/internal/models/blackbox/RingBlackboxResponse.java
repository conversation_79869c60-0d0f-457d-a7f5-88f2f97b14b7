package com.sumo.offer.internal.models.blackbox;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RingBlackboxResponse {
    @JsonProperty("interest_rate")
    private double interestRate;

    @JsonProperty("score")
    private int score;

    @JsonProperty("approval_flag")
    private String approvalFlag;

    @JsonProperty("offer_amount")
    private double offerAmount;

    @JsonProperty("processing_fee")
    private double processingFee;

    @JsonProperty("pl_type")
    private String plType;

    @JsonProperty("tenure")
    private int tenure;

    @JsonProperty("unique_id")
    private String uniqueId;

    @JsonProperty("status_code")
    private int statusCode;

    @JsonProperty("message")
    private String message;

    @JsonProperty("rejection_reason")
    private String rejectionReason;
}
