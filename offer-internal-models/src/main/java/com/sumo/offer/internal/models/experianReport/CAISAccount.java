package com.sumo.offer.internal.models.experianReport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CAISAccount {

    @JacksonXmlProperty(localName = "CAIS_Account_DETAILS")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<CAISAccountDetails> caisAccountDetails;
}
