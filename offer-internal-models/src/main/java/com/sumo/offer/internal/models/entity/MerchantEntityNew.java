package com.sumo.offer.internal.models.entity;

import com.flipkart.fintech.pinaka.api.enums.MerchantStatus;
import lombok.Data;

import jakarta.persistence.*;


@Entity
@Table(name = "merchants")
@Data
public class MerchantEntityNew extends BaseEntity {

    @Column(name = "merchant_key")
    private String merchantKey;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private MerchantStatus status;


}
