package com.sumo.offer.internal.models.response;


import com.sumo.offer.internal.models.LenderOfferEntity;
import com.sumo.offer.internal.models.responsemetadata.FilterServiceFilterMetadata;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class FilterServiceFilterResponse {
    List<LenderOfferEntity> offers;
    FilterServiceFilterMetadata filterServiceFilterMetadata;
}


