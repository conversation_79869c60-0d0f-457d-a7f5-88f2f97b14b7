package com.sumo.offer.internal.models;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import jakarta.persistence.*;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

@Entity
@Data
@Table(name = "lender_pincode_serviceability")
public class LenderPincodeServiceabilityEntity{

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "pincode")
    private String pincode;

    @Enumerated(EnumType.STRING)
    @Column(name = "lender")
    private Lender lender;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "created_at_ms")
    private Long createdAtMS;

    @Column(name = "updated_at_ms")
    private Long updatedAtMS;

    @Enumerated(EnumType.STRING)
    @Column(name = "product_type")
    private OrchestratorProductType productType;

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        this.createdAtMS = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
        this.updatedAtMS = this.createdAtMS;
    }

    @PreUpdate
    protected void onUpdate() {
        Instant now = Instant.now();
        this.updatedAtMS = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
    }

    public LocalDateTime getCreatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(createdAtMS), ZoneId.of("Asia/Kolkata"));
    }

    public LocalDateTime getUpdatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(createdAtMS), ZoneId.of("Asia/Kolkata"));
    }

    public LenderPincodeServiceabilityEntity() {

    }

    public LenderPincodeServiceabilityEntity(String pincode, Lender lender, OrchestratorProductType productType,
                             Boolean active) {
        this.id = Utility.generateHashedId("LPS");
        this.lender = lender;
        this.active = active;
        this.pincode = pincode;
        this.productType = productType;
    }

}
