package com.sumo.offer.internal.models.entity.compositeKeys;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 * Composite Primary Key Class for EtbUsersEntity
 *
 */
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class EtbUserId implements Serializable {

    private String smUserId;

    private Lender lender;
}
