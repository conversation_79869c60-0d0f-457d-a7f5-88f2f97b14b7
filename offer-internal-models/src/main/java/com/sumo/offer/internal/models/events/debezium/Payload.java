package com.sumo.offer.internal.models.events.debezium;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Payload<T>{
    private T before;
    private T after;
    private Source source;
    private String op;
    @JsonProperty("ts_ms")
    private Long tsMs;
    private Transaction transaction;
}
