package com.sumo.offer.internal.models.blackbox;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MoneyviewBlackboxResponseDetail {

    private String leadUserRef;
    private String status;
    private String bestOfferAmount;
    private String bestOfferTenure;
    private String bestOfferRoi;
    private String expiryDate;
    private String category;
    private String leadSource;
}
