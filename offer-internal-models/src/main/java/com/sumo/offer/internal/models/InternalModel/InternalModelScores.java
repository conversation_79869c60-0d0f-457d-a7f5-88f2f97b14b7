package com.sumo.offer.internal.models.InternalModel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@Getter
public class InternalModelScores {
    @JsonProperty("score")
    private Double score;
    @JsonProperty("disbursal_score")
    private Double propensityScore;
    @JsonProperty("approval_score")
    private Double approvalScore;

}
