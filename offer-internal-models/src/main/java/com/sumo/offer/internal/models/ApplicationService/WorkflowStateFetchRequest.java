package com.sumo.offer.internal.models.ApplicationService;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class WorkflowStateFetchRequest {
    @JsonProperty("external_user_id")
    private String externalUserId;

    @JsonProperty("sm_user_id")
    private String smUserId;

    @JsonProperty("application_type")
    private String applicationType;

    @JsonProperty("workflow_states")
    private List<WorkflowState> workflowStates;

    public WorkflowStateFetchRequest(String merchantUserId, String smUserId, String applcationType, List<WorkflowState> states) {
        this.externalUserId = merchantUserId;
        this.smUserId = smUserId;
        this.applicationType = applcationType;
        this.workflowStates = states;
    }
}
