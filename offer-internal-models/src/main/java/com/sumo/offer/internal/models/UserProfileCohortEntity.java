package com.sumo.offer.internal.models;

import lombok.Data;
import jakarta.persistence.*;


@Entity
@Table(name = "user_profile_cohort")
@Data
public class UserProfileCohortEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "version")
    private String version;

    public UserProfileCohortEntity() {
    }

    public UserProfileCohortEntity(Long id, String name, Boolean active, String version) {
        this.name = name;
        this.active = active;
        this.version = version;
    }

}
