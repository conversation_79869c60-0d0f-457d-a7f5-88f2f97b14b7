package com.sumo.offer.internal.models;

import java.util.UUID;

public class Utility {

    public static String generateHashedId(String prefix) {
        String uuid = UUID.randomUUID().toString().toUpperCase().replace("-", "");
        int maxLength = 15 - prefix.length();
        if (uuid.length() > maxLength) {
            uuid = uuid.substring(0, maxLength);
        }
        return prefix.toUpperCase() + uuid;
    }
}