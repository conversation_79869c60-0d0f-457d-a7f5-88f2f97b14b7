package com.sumo.offer.internal.models;


import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;

@Entity
@Data
@Table(name = "axis_dedupe")
@IdClass(AxisDuplicateLoanId.class)
public class AxisDuplicateLoan {
    @Id
    @Column(name="account_id")
    private String accountId;

    @Id
    @Column(name = "lender")
    private String lender;

    @Column(name = "created_at")
    private Long createdAt;

    @Enumerated(EnumType.STRING)
    @Column(name = "product_type")
    private OrchestratorProductType productType;
}


class AxisDuplicateLoanId implements Serializable {

    private String accountId;
    private String lender;
}