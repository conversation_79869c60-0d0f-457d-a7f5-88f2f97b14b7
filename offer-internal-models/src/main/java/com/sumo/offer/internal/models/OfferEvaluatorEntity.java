package com.sumo.offer.internal.models;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;


import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;


@Entity
@Table(name = "offer_evaluator")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class OfferEvaluatorEntity {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "user_profile_cohort_id")
    private String userProfileCohortId;

    @Getter
    @Column(name = "weight")
    private Double weight;

    @Getter
    @Column(name = "lenders")
    private String lenders;

    @Column(name = "version")
    private Double version;
    // will store lender rank as config
    @Column(name = "created_by")
    private String created_by;

    @Column(name = "created_at_ms")
    private Long created_at_ms;


    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        this.created_at_ms = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
    }

    @PreUpdate
    protected void onUpdate() {
        Instant now = Instant.now();
    }

    public LocalDateTime getCreatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(created_at_ms), ZoneId.of("Asia/Kolkata"));
    }

    public LocalDateTime getUpdatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(created_at_ms), ZoneId.of("Asia/Kolkata"));
    }

    public OfferEvaluatorEntity(){
        this.id = Utility.generateHashedId("OEE");
    }

}
