package com.sumo.offer.internal.models.events.debezium;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Source {
    private String version;
    private String connector;
    private String name;
    @JsonProperty("ts_ms")
    private Long tsMs;
    private String snapshot;
    private String db;
    private String sequence;
    private String table;
    private Long serverId;
    private String gtid;
    private String file;
    private Long pos;
    private Integer row;
    private Long thread;
    private String query;
}
