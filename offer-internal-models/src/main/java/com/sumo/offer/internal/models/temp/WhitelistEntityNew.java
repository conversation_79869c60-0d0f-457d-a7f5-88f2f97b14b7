package com.sumo.offer.internal.models.temp;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.sumo.offer.internal.models.entity.BaseEntity;
import com.sumo.offer.internal.models.entity.MerchantEntityNew;
import lombok.Data;

import jakarta.persistence.*;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 06/11/17.
 */
@Entity
@Table(name = "whitelist")
@Data
public class WhitelistEntityNew extends BaseEntity {

    @Column(name = "whitelist_name")
    private String whitelistName;

    @Column(name = "whitelist_desc")
    private String whitelistDesc;

    @Column(name = "product_type")
    @Enumerated(EnumType.STRING)
    private ProductType productType;

    @Column(name = "lender")
    private String lender;

    @Column(name = "meta_data")
    private String metaData;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "merchant_id", nullable = false)
    private MerchantEntityNew merchant;

    @Column(name = "rule_configs")
    private String ruleConfigs;

    @Column(name = "enabled")
    private boolean enabled;

}
