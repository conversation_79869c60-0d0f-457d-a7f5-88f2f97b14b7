package com.sumo.offer.internal.models;

import lombok.Data;

import jakarta.persistence.*;
@Entity
@Data
@Table(name = "blacklisted_users")
public class BlacklistedEntity {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "created_at_ms")
    private Long createdAtMS;

    @Column(name = "updated_at_ms")
    private Long updatedAtMS;
}
