package com.sumo.offer.internal.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.sumo.offer.internal.models.temp.BorrowerEntityNew;
import lombok.Data;

import jakarta.persistence.*;
import java.time.Instant;
import java.time.ZoneId;
import java.util.HashMap;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LenderOfferEntity {

    private String id;

    private String userId;

    private String userProfileId;

    private Lender lender;

    private LenderOfferState status;

    private LenderOfferType offerType;

    private String offerDetails;

    private HashMap<String, Object> lenderDetails;

    private Long createdAtMS;

    private Long updatedAtMS;

    private String metadata;

    private Long amount;

    private Double roi;

    private OrchestratorProductType productType;

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        this.createdAtMS = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
        this.updatedAtMS = this.createdAtMS;
    }

    @PreUpdate
    protected void onUpdate() {
        Instant now = Instant.now();
        this.updatedAtMS = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
    }

    public LenderOfferEntity(String userId, String userProfileId, Lender lender, LenderOfferState status,
                             LenderOfferType offerType, String metadata, OrchestratorProductType orchestratorProductType) {
        this.id = Utility.generateHashedId("LO");
        this.userId = userId;
        this.userProfileId = userProfileId;
        this.lender = lender;
        this.status = status;
        this.offerType = offerType;
        this.productType = orchestratorProductType;
        this.metadata = metadata;
        this.lenderDetails = new HashMap<>();
    }

    public LenderOfferEntity(String userId, String userProfileId, Lender lender, LenderOfferState status,
                             LenderOfferType offerType, Long amount, Double roi) {
        this.id = Utility.generateHashedId("LO");
        this.userId = userId;
        this.userProfileId = userProfileId;
        this.lender = lender;
        this.status = status;
        this.offerType = offerType;
        this.amount = amount;
        this.roi = roi;
        this.lenderDetails = new HashMap<>();
    }

    public LenderOfferEntity() {

    }

    private LenderOfferState mapEnabledToOfferState(boolean enabled) {
        return enabled ? LenderOfferState.ACTIVE : LenderOfferState.INACTIVE;
    }
}
