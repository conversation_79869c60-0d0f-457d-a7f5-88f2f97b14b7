package com.sumo.offer.internal.models.events;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sumo.offer.internal.models.entity.PreapprovedOffersEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PreapprovedOffersEventPayload extends PreapprovedOffersEntity {
}
