package com.sumo.offer.internal.models.request;

import jakarta.json.bind.annotation.JsonbDateFormat;
import lombok.*;

import java.sql.Timestamp;

@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Getter
@Setter
public class UpdatePreApprovedOfferExpiryDateRequest {

    private String smUserId;

    private String offerId;

    @JsonbDateFormat("yyyy-MM-dd HH:mm:ss.S")
    private Timestamp expiryDate;
}
