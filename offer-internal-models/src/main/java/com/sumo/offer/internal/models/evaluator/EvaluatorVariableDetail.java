package com.sumo.offer.internal.models.evaluator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import jakarta.persistence.*;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = "evaluator_variable_detail")
@Entity
public class EvaluatorVariableDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "variable")
    @Enumerated(EnumType.STRING)
    private EvaluatorVariableName variable;

    @Column(name = "cohort_id")
    private String cohortId;

    @Column(name = "offer_types")
    private String offerTypes;

    @Column(name = "value")
    private Double value;

    @Column(name = "lender")
    @Enumerated(EnumType.STRING)
    private Lender lender;

    @Column(name = "version")
    private Double version;

    @Column(name = "created_at_ms")
    private Long createdAt;

    @Column(name = "created_by")
    private String createdBy;

}
