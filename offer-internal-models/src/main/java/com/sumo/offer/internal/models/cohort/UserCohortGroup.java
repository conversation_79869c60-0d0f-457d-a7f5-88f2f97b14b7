package com.sumo.offer.internal.models.cohort;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = "user_cohort_group")
@Entity
public class UserCohortGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "version")
    private Double version;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at_ms")
    private String createdAt;

    @Column(name = "description")
    private String description;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "group_id", referencedColumnName = "id")
    private List<UserCohort> userCohorts;
}
