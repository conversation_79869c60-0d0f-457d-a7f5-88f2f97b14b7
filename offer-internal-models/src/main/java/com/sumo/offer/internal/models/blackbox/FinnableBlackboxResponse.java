package com.sumo.offer.internal.models.blackbox;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FinnableBlackboxResponse {

    @JsonProperty("body")
    private Body body;

    @JsonProperty("status")
    private Status status;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Body {

        @JsonProperty("interest_rate")
        private Double interestRate;

        @JsonProperty("offer_amount")
        private Long offerAmount;

        @JsonProperty("rejectreason")
        private String rejectReason;

        @JsonProperty("tenure")
        private Integer tenure;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Status {

        @JsonProperty("code")
        private Integer code;

        @JsonProperty("message")
        private String message;
    }
}
