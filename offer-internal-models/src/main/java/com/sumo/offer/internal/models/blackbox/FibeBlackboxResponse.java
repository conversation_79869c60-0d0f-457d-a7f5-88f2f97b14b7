package com.sumo.offer.internal.models.blackbox;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FibeBlackboxResponse extends BlackboxResponse{

    @JsonProperty("ROI")
    private Double rateOfInterest;
    @JsonProperty("a_score")
    private String aScore;
    @JsonProperty("approval_flag")
    private String approvalFlag;
    @JsonProperty("limit")
    private Long limit;
    @JsonProperty("pl_type")
    private String plType;
    @JsonProperty("rejection_reason")
    private String rejectionReason;
    @JsonProperty("tenure")
    private Integer tenure;
    @JsonProperty("unique_id")
    private String uniqueId;
}
