package com.sumo.offer.internal.models.experianReport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CAISAccountDetails {

    @JacksonXmlProperty(localName = "CAIS_Holder_Details")
    CAISHolderDetails caisHolderDetails;

    @JacksonXmlProperty(localName = "CAIS_Holder_Address_Details")
    CAISHolderAddressDetails caisHolderAddressDetails;
}
