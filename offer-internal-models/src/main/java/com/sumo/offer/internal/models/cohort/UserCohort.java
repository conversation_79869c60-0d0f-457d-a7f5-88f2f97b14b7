package com.sumo.offer.internal.models.cohort;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sumo.offer.internal.models.filterservice.Filter;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = "user_cohort")
@Entity
public class UserCohort {

    private static ObjectMapper mapper = new ObjectMapper();

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "group_id")
    private String groupId;

    @Column(name = "rules")
    private String rules;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at_ms")
    private Long createdAt;

    @Column(name = "name")
    private String name;

    public List<Filter> getRules() {
        if(rules == null) return null;
        try {
            return mapper.readValue(rules, new TypeReference<List<Filter>>() {});
        } catch (Exception e) {
            throw new RuntimeException(String.format("UserCohortRules::Unable to parse rules for id %s", this.id));
        }
    }

    public void setRules(List<Filter> rules) {
        if(rules == null) this.rules = null;
        try {
            this.rules = mapper.writeValueAsString(rules);
        } catch (Exception e) {
            throw new RuntimeException(String.format("UserCohortRules::Unable to write rules for id %s", this.id));
        }
    }

}
