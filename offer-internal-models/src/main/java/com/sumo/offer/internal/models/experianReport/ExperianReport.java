package com.sumo.offer.internal.models.experianReport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.util.List;

@JacksonXmlRootElement(localName = "INProfileResponse")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ExperianReport {

    @JacksonXmlProperty(localName = "CAIS_Account")
    private CAISAccount caisAccount;

}
