package com.sumo.offer.internal.models.cohort;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class UserCohortGroupCreateRequest {

    @JsonProperty("description")
    private String description;

    @JsonProperty("cohorts")
    private List<UserCohortRequest> cohorts;

}
