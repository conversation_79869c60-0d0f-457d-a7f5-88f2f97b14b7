package com.sumo.offer.internal.models.experianReport;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.htrace.shaded.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;

import static org.junit.jupiter.api.Assertions.*;

public class ExperianReportParserTest {

    @Test
    public void parse(){

        ExperianReport experianReport;
        try {
            XmlMapper xmlMapper = new XmlMapper();
            //String json = new ObjectMapper().jso
            String  report = StringEscapeUtils.unescapeXml("&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;yes&quot;?&gt;&lt;INProfileResponse&gt;&lt;Header&gt;&lt;SystemCode&gt;0&lt;/SystemCode&gt;&lt;MessageText&gt;&lt;/MessageText&gt;&lt;ReportDate&gt;20240621&lt;/ReportDate&gt;&lt;ReportTime&gt;082625&lt;/ReportTime&gt;&lt;/Header&gt;&lt;UserMessage&gt;&lt;UserMessageText&gt;Normal Response&lt;/UserMessageText&gt;&lt;/UserMessage&gt;&lt;CreditProfileHeader&gt;&lt;Enquiry_Username&gt;customized_match_v3__supermoney_~DS&lt;/Enquiry_Username&gt;&lt;ReportDate&gt;20240621&lt;/ReportDate&gt;&lt;ReportTime&gt;082625&lt;/ReportTime&gt;&lt;Version&gt;V2.4&lt;/Version&gt;&lt;ReportNumber&gt;1718938584784&lt;/ReportNumber&gt;&lt;Subscriber&gt;&lt;/Subscriber&gt;&lt;Subscriber_Name&gt;Bureau Disclosure Report with Customized Match V3&lt;/Subscriber_Name&gt;&lt;/CreditProfileHeader&gt;&lt;Current_Application&gt;&lt;Current_Application_Details&gt;&lt;Enquiry_Reason&gt;6&lt;/Enquiry_Reason&gt;&lt;Finance_Purpose&gt;&lt;/Finance_Purpose&gt;&lt;Amount_Financed&gt;0&lt;/Amount_Financed&gt;&lt;Duration_Of_Agreement&gt;0&lt;/Duration_Of_Agreement&gt;&lt;Current_Applicant_Details&gt;&lt;Last_Name&gt;DEEPU&lt;/Last_Name&gt;&lt;First_Name&gt;DEEPU&lt;/First_Name&gt;&lt;Middle_Name1&gt;&lt;/Middle_Name1&gt;&lt;Middle_Name2&gt;&lt;/Middle_Name2&gt;&lt;Middle_Name3&gt;&lt;/Middle_Name3&gt;&lt;Gender_Code&gt;&lt;/Gender_Code&gt;&lt;IncomeTaxPan&gt;**********&lt;/IncomeTaxPan&gt;&lt;PAN_Issue_Date&gt;&lt;/PAN_Issue_Date&gt;&lt;PAN_Expiration_Date&gt;&lt;/PAN_Expiration_Date&gt;&lt;Passport_number&gt;&lt;/Passport_number&gt;&lt;Passport_Issue_Date&gt;&lt;/Passport_Issue_Date&gt;&lt;Passport_Expiration_Date&gt;&lt;/Passport_Expiration_Date&gt;&lt;Voter_s_Identity_Card&gt;&lt;/Voter_s_Identity_Card&gt;&lt;Voter_ID_Issue_Date&gt;&lt;/Voter_ID_Issue_Date&gt;&lt;Voter_ID_Expiration_Date&gt;&lt;/Voter_ID_Expiration_Date&gt;&lt;Driver_License_Number&gt;&lt;/Driver_License_Number&gt;&lt;Driver_License_Issue_Date&gt;&lt;/Driver_License_Issue_Date&gt;&lt;Driver_License_Expiration_Date&gt;&lt;/Driver_License_Expiration_Date&gt;&lt;Ration_Card_Number&gt;&lt;/Ration_Card_Number&gt;&lt;Ration_Card_Issue_Date&gt;&lt;/Ration_Card_Issue_Date&gt;&lt;Ration_Card_Expiration_Date&gt;&lt;/Ration_Card_Expiration_Date&gt;&lt;Universal_ID_Number&gt;&lt;/Universal_ID_Number&gt;&lt;Universal_ID_Issue_Date&gt;&lt;/Universal_ID_Issue_Date&gt;&lt;Universal_ID_Expiration_Date&gt;&lt;/Universal_ID_Expiration_Date&gt;&lt;Date_Of_Birth_Applicant&gt;&lt;/Date_Of_Birth_Applicant&gt;&lt;Telephone_Number_Applicant_1st&gt;&lt;/Telephone_Number_Applicant_1st&gt;&lt;Telephone_Extension&gt;&lt;/Telephone_Extension&gt;&lt;Telephone_Type&gt;&lt;/Telephone_Type&gt;&lt;MobilePhoneNumber&gt;**********&lt;/MobilePhoneNumber&gt;&lt;EMailId&gt;<EMAIL>&lt;/EMailId&gt;&lt;/Current_Applicant_Details&gt;&lt;Current_Other_Details&gt;&lt;Income&gt;0&lt;/Income&gt;&lt;Marital_Status&gt;&lt;/Marital_Status&gt;&lt;Employment_Status&gt;&lt;/Employment_Status&gt;&lt;Time_with_Employer&gt;&lt;/Time_with_Employer&gt;&lt;Number_of_Major_Credit_Card_Held&gt;&lt;/Number_of_Major_Credit_Card_Held&gt;&lt;/Current_Other_Details&gt;&lt;Current_Applicant_Address_Details&gt;&lt;FlatNoPlotNoHouseNo&gt;&lt;/FlatNoPlotNoHouseNo&gt;&lt;BldgNoSocietyName&gt;&lt;/BldgNoSocietyName&gt;&lt;RoadNoNameAreaLocality&gt;&lt;/RoadNoNameAreaLocality&gt;&lt;City&gt;&lt;/City&gt;&lt;Landmark&gt;&lt;/Landmark&gt;&lt;State&gt;&lt;/State&gt;&lt;PINCode&gt;&lt;/PINCode&gt;&lt;Country_Code&gt;IB&lt;/Country_Code&gt;&lt;/Current_Applicant_Address_Details&gt;&lt;Current_Applicant_Additional_AddressDetails/&gt;&lt;/Current_Application_Details&gt;&lt;/Current_Application&gt;&lt;CAIS_Account&gt;&lt;CAIS_Summary&gt;&lt;Credit_Account&gt;&lt;CreditAccountTotal&gt;2&lt;/CreditAccountTotal&gt;&lt;CreditAccountActive&gt;1&lt;/CreditAccountActive&gt;&lt;CreditAccountDefault&gt;0&lt;/CreditAccountDefault&gt;&lt;CreditAccountClosed&gt;1&lt;/CreditAccountClosed&gt;&lt;CADSuitFiledCurrentBalance&gt;0&lt;/CADSuitFiledCurrentBalance&gt;&lt;/Credit_Account&gt;&lt;Total_Outstanding_Balance&gt;&lt;Outstanding_Balance_Secured&gt;0&lt;/Outstanding_Balance_Secured&gt;&lt;Outstanding_Balance_Secured_Percentage&gt;0&lt;/Outstanding_Balance_Secured_Percentage&gt;&lt;Outstanding_Balance_UnSecured&gt;10875&lt;/Outstanding_Balance_UnSecured&gt;&lt;Outstanding_Balance_UnSecured_Percentage&gt;100&lt;/Outstanding_Balance_UnSecured_Percentage&gt;&lt;Outstanding_Balance_All&gt;10875&lt;/Outstanding_Balance_All&gt;&lt;/Total_Outstanding_Balance&gt;&lt;/CAIS_Summary&gt;&lt;CAIS_Account_DETAILS&gt;&lt;Identification_Number&gt;NBFHOM2832&lt;/Identification_Number&gt;&lt;Subscriber_Name&gt;Home Credit India Finance Pvt. Ltd&lt;/Subscriber_Name&gt;&lt;Account_Number&gt;XXXXXX5922&lt;/Account_Number&gt;&lt;Portfolio_Type&gt;I&lt;/Portfolio_Type&gt;&lt;Account_Type&gt;06&lt;/Account_Type&gt;&lt;Open_Date&gt;********&lt;/Open_Date&gt;&lt;Credit_Limit_Amount&gt;&lt;/Credit_Limit_Amount&gt;&lt;Highest_Credit_or_Original_Loan_Amount&gt;15992&lt;/Highest_Credit_or_Original_Loan_Amount&gt;&lt;Terms_Duration&gt;&lt;/Terms_Duration&gt;&lt;Terms_Frequency&gt;&lt;/Terms_Frequency&gt;&lt;Scheduled_Monthly_Payment_Amount&gt;&lt;/Scheduled_Monthly_Payment_Amount&gt;&lt;Account_Status&gt;13&lt;/Account_Status&gt;&lt;Payment_Rating&gt;0&lt;/Payment_Rating&gt;&lt;Payment_History_Profile&gt;0??00???????????????????????????????&lt;/Payment_History_Profile&gt;&lt;Special_Comment&gt;&lt;/Special_Comment&gt;&lt;Current_Balance&gt;0&lt;/Current_Balance&gt;&lt;Amount_Past_Due&gt;0&lt;/Amount_Past_Due&gt;&lt;Original_Charge_Off_Amount&gt;&lt;/Original_Charge_Off_Amount&gt;&lt;Date_Reported&gt;********&lt;/Date_Reported&gt;&lt;Date_of_First_Delinquency&gt;&lt;/Date_of_First_Delinquency&gt;&lt;Date_Closed&gt;********&lt;/Date_Closed&gt;&lt;Date_of_Last_Payment&gt;********&lt;/Date_of_Last_Payment&gt;&lt;SuitFiledWillfulDefaultWrittenOffStatus&gt;&lt;/SuitFiledWillfulDefaultWrittenOffStatus&gt;&lt;SuitFiled_WilfulDefault&gt;&lt;/SuitFiled_WilfulDefault&gt;&lt;Written_off_Settled_Status&gt;&lt;/Written_off_Settled_Status&gt;&lt;Value_of_Credits_Last_Month&gt;&lt;/Value_of_Credits_Last_Month&gt;&lt;Occupation_Code&gt;&lt;/Occupation_Code&gt;&lt;Settlement_Amount&gt;&lt;/Settlement_Amount&gt;&lt;Value_of_Collateral&gt;&lt;/Value_of_Collateral&gt;&lt;Type_of_Collateral&gt;&lt;/Type_of_Collateral&gt;&lt;Written_Off_Amt_Total&gt;&lt;/Written_Off_Amt_Total&gt;&lt;Written_Off_Amt_Principal&gt;&lt;/Written_Off_Amt_Principal&gt;&lt;Rate_of_Interest&gt;&lt;/Rate_of_Interest&gt;&lt;Repayment_Tenure&gt;0&lt;/Repayment_Tenure&gt;&lt;Promotional_Rate_Flag&gt;&lt;/Promotional_Rate_Flag&gt;&lt;Income&gt;&lt;/Income&gt;&lt;Income_Indicator&gt;&lt;/Income_Indicator&gt;&lt;Income_Frequency_Indicator&gt;&lt;/Income_Frequency_Indicator&gt;&lt;DefaultStatusDate&gt;&lt;/DefaultStatusDate&gt;&lt;LitigationStatusDate&gt;&lt;/LitigationStatusDate&gt;&lt;WriteOffStatusDate&gt;&lt;/WriteOffStatusDate&gt;&lt;DateOfAddition&gt;********&lt;/DateOfAddition&gt;&lt;CurrencyCode&gt;INR&lt;/CurrencyCode&gt;&lt;Subscriber_comments&gt;&lt;/Subscriber_comments&gt;&lt;Consumer_comments&gt;&lt;/Consumer_comments&gt;&lt;AccountHoldertypeCode&gt;1&lt;/AccountHoldertypeCode&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2023&lt;/Year&gt;&lt;Month&gt;01&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;S&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2022&lt;/Year&gt;&lt;Month&gt;12&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;S&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2022&lt;/Year&gt;&lt;Month&gt;09&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;S&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2022&lt;/Year&gt;&lt;Month&gt;08&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Holder_Details&gt;&lt;Surname_Non_Normalized&gt;DEEPU&lt;/Surname_Non_Normalized&gt;&lt;First_Name_Non_Normalized&gt;&lt;/First_Name_Non_Normalized&gt;&lt;Middle_Name_1_Non_Normalized&gt;&lt;/Middle_Name_1_Non_Normalized&gt;&lt;Middle_Name_2_Non_Normalized&gt;&lt;/Middle_Name_2_Non_Normalized&gt;&lt;Middle_Name_3_Non_Normalized&gt;&lt;/Middle_Name_3_Non_Normalized&gt;&lt;Alias&gt;&lt;/Alias&gt;&lt;Gender_Code&gt;1&lt;/Gender_Code&gt;&lt;Income_TAX_PAN&gt;XXXXXX385K&lt;/Income_TAX_PAN&gt;&lt;Passport_Number&gt;&lt;/Passport_Number&gt;&lt;Voter_ID_Number&gt;&lt;/Voter_ID_Number&gt;&lt;Date_of_birth&gt;********&lt;/Date_of_birth&gt;&lt;/CAIS_Holder_Details&gt;&lt;CAIS_Holder_Address_Details&gt;&lt;First_Line_Of_Address_non_normalized&gt;NEAR SINGH SABHA GURDWARA MITHAPUR&lt;/First_Line_Of_Address_non_normalized&gt;&lt;Second_Line_Of_Address_non_normalized&gt;JALANDHAR  NEAR SINGH SABHA GURDWARA&lt;/Second_Line_Of_Address_non_normalized&gt;&lt;Third_Line_Of_Address_non_normalized&gt;JALANDHAR&lt;/Third_Line_Of_Address_non_normalized&gt;&lt;City_non_normalized&gt;&lt;/City_non_normalized&gt;&lt;Fifth_Line_Of_Address_non_normalized&gt;&lt;/Fifth_Line_Of_Address_non_normalized&gt;&lt;State_non_normalized&gt;03&lt;/State_non_normalized&gt;&lt;ZIP_Postal_Code_non_normalized&gt;144022&lt;/ZIP_Postal_Code_non_normalized&gt;&lt;CountryCode_non_normalized&gt;IB&lt;/CountryCode_non_normalized&gt;&lt;Address_indicator_non_normalized&gt;02&lt;/Address_indicator_non_normalized&gt;&lt;Residence_code_non_normalized&gt;&lt;/Residence_code_non_normalized&gt;&lt;/CAIS_Holder_Address_Details&gt;&lt;CAIS_Holder_Phone_Details&gt;&lt;Telephone_Number&gt;&lt;/Telephone_Number&gt;&lt;Telephone_Type&gt;01&lt;/Telephone_Type&gt;&lt;Telephone_Extension&gt;&lt;/Telephone_Extension&gt;&lt;Mobile_Telephone_Number&gt;XXXXX58942&lt;/Mobile_Telephone_Number&gt;&lt;FaxNumber&gt;&lt;/FaxNumber&gt;&lt;EMailId&gt;&lt;/EMailId&gt;&lt;/CAIS_Holder_Phone_Details&gt;&lt;CAIS_Holder_ID_Details&gt;&lt;Income_TAX_PAN&gt;XXXXXX385K&lt;/Income_TAX_PAN&gt;&lt;PAN_Issue_Date&gt;&lt;/PAN_Issue_Date&gt;&lt;PAN_Expiration_Date&gt;&lt;/PAN_Expiration_Date&gt;&lt;Passport_Number&gt;&lt;/Passport_Number&gt;&lt;Passport_Issue_Date&gt;&lt;/Passport_Issue_Date&gt;&lt;Passport_Expiration_Date&gt;&lt;/Passport_Expiration_Date&gt;&lt;Voter_ID_Number&gt;&lt;/Voter_ID_Number&gt;&lt;Voter_ID_Issue_Date&gt;&lt;/Voter_ID_Issue_Date&gt;&lt;Voter_ID_Expiration_Date&gt;&lt;/Voter_ID_Expiration_Date&gt;&lt;Driver_License_Number&gt;&lt;/Driver_License_Number&gt;&lt;Driver_License_Issue_Date&gt;&lt;/Driver_License_Issue_Date&gt;&lt;Driver_License_Expiration_Date&gt;&lt;/Driver_License_Expiration_Date&gt;&lt;Ration_Card_Number&gt;&lt;/Ration_Card_Number&gt;&lt;Ration_Card_Issue_Date&gt;&lt;/Ration_Card_Issue_Date&gt;&lt;Ration_Card_Expiration_Date&gt;&lt;/Ration_Card_Expiration_Date&gt;&lt;Universal_ID_Number&gt;&lt;/Universal_ID_Number&gt;&lt;Universal_ID_Issue_Date&gt;&lt;/Universal_ID_Issue_Date&gt;&lt;Universal_ID_Expiration_Date&gt;&lt;/Universal_ID_Expiration_Date&gt;&lt;EMailId&gt;&lt;/EMailId&gt;&lt;/CAIS_Holder_ID_Details&gt;&lt;/CAIS_Account_DETAILS&gt;&lt;CAIS_Account_DETAILS&gt;&lt;Identification_Number&gt;NBFHDBFS53&lt;/Identification_Number&gt;&lt;Subscriber_Name&gt;HDB Financial Services Limited&lt;/Subscriber_Name&gt;&lt;Account_Number&gt;XXXX8399&lt;/Account_Number&gt;&lt;Portfolio_Type&gt;I&lt;/Portfolio_Type&gt;&lt;Account_Type&gt;06&lt;/Account_Type&gt;&lt;Open_Date&gt;********&lt;/Open_Date&gt;&lt;Credit_Limit_Amount&gt;&lt;/Credit_Limit_Amount&gt;&lt;Highest_Credit_or_Original_Loan_Amount&gt;41946&lt;/Highest_Credit_or_Original_Loan_Amount&gt;&lt;Terms_Duration&gt;012&lt;/Terms_Duration&gt;&lt;Terms_Frequency&gt;M&lt;/Terms_Frequency&gt;&lt;Scheduled_Monthly_Payment_Amount&gt;&lt;/Scheduled_Monthly_Payment_Amount&gt;&lt;Account_Status&gt;97&lt;/Account_Status&gt;&lt;Payment_Rating&gt;5&lt;/Payment_Rating&gt;&lt;Payment_History_Profile&gt;**********??????????????????????????&lt;/Payment_History_Profile&gt;&lt;Special_Comment&gt;&lt;/Special_Comment&gt;&lt;Current_Balance&gt;10875&lt;/Current_Balance&gt;&lt;Amount_Past_Due&gt;13057&lt;/Amount_Past_Due&gt;&lt;Original_Charge_Off_Amount&gt;&lt;/Original_Charge_Off_Amount&gt;&lt;Date_Reported&gt;********&lt;/Date_Reported&gt;&lt;Date_of_First_Delinquency&gt;&lt;/Date_of_First_Delinquency&gt;&lt;Date_Closed&gt;&lt;/Date_Closed&gt;&lt;Date_of_Last_Payment&gt;********&lt;/Date_of_Last_Payment&gt;&lt;SuitFiledWillfulDefaultWrittenOffStatus&gt;&lt;/SuitFiledWillfulDefaultWrittenOffStatus&gt;&lt;SuitFiled_WilfulDefault&gt;&lt;/SuitFiled_WilfulDefault&gt;&lt;Written_off_Settled_Status&gt;02&lt;/Written_off_Settled_Status&gt;&lt;Value_of_Credits_Last_Month&gt;&lt;/Value_of_Credits_Last_Month&gt;&lt;Occupation_Code&gt;&lt;/Occupation_Code&gt;&lt;Settlement_Amount&gt;&lt;/Settlement_Amount&gt;&lt;Value_of_Collateral&gt;&lt;/Value_of_Collateral&gt;&lt;Type_of_Collateral&gt;&lt;/Type_of_Collateral&gt;&lt;Written_Off_Amt_Total&gt;16361&lt;/Written_Off_Amt_Total&gt;&lt;Written_Off_Amt_Principal&gt;10875&lt;/Written_Off_Amt_Principal&gt;&lt;Rate_of_Interest&gt;&lt;/Rate_of_Interest&gt;&lt;Repayment_Tenure&gt;12&lt;/Repayment_Tenure&gt;&lt;Promotional_Rate_Flag&gt;&lt;/Promotional_Rate_Flag&gt;&lt;Income&gt;&lt;/Income&gt;&lt;Income_Indicator&gt;&lt;/Income_Indicator&gt;&lt;Income_Frequency_Indicator&gt;&lt;/Income_Frequency_Indicator&gt;&lt;DefaultStatusDate&gt;&lt;/DefaultStatusDate&gt;&lt;LitigationStatusDate&gt;&lt;/LitigationStatusDate&gt;&lt;WriteOffStatusDate&gt;&lt;/WriteOffStatusDate&gt;&lt;DateOfAddition&gt;********&lt;/DateOfAddition&gt;&lt;CurrencyCode&gt;INR&lt;/CurrencyCode&gt;&lt;Subscriber_comments&gt;&lt;/Subscriber_comments&gt;&lt;Consumer_comments&gt;&lt;/Consumer_comments&gt;&lt;AccountHoldertypeCode&gt;1&lt;/AccountHoldertypeCode&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2024&lt;/Year&gt;&lt;Month&gt;05&lt;/Month&gt;&lt;Days_Past_Due&gt;151&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2024&lt;/Year&gt;&lt;Month&gt;04&lt;/Month&gt;&lt;Days_Past_Due&gt;120&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2024&lt;/Year&gt;&lt;Month&gt;03&lt;/Month&gt;&lt;Days_Past_Due&gt;90&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2024&lt;/Year&gt;&lt;Month&gt;02&lt;/Month&gt;&lt;Days_Past_Due&gt;59&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2024&lt;/Year&gt;&lt;Month&gt;01&lt;/Month&gt;&lt;Days_Past_Due&gt;30&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2023&lt;/Year&gt;&lt;Month&gt;12&lt;/Month&gt;&lt;Days_Past_Due&gt;30&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2023&lt;/Year&gt;&lt;Month&gt;11&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2023&lt;/Year&gt;&lt;Month&gt;10&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2023&lt;/Year&gt;&lt;Month&gt;09&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2023&lt;/Year&gt;&lt;Month&gt;08&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Account_History&gt;&lt;Year&gt;2023&lt;/Year&gt;&lt;Month&gt;07&lt;/Month&gt;&lt;Days_Past_Due&gt;0&lt;/Days_Past_Due&gt;&lt;Asset_Classification&gt;?&lt;/Asset_Classification&gt;&lt;/CAIS_Account_History&gt;&lt;CAIS_Holder_Details&gt;&lt;Surname_Non_Normalized&gt;DEEPU SO VIJAYYADAV&lt;/Surname_Non_Normalized&gt;&lt;First_Name_Non_Normalized&gt;&lt;/First_Name_Non_Normalized&gt;&lt;Middle_Name_1_Non_Normalized&gt;SO&lt;/Middle_Name_1_Non_Normalized&gt;&lt;Middle_Name_2_Non_Normalized&gt;&lt;/Middle_Name_2_Non_Normalized&gt;&lt;Middle_Name_3_Non_Normalized&gt;&lt;/Middle_Name_3_Non_Normalized&gt;&lt;Alias&gt;&lt;/Alias&gt;&lt;Gender_Code&gt;1&lt;/Gender_Code&gt;&lt;Income_TAX_PAN&gt;XXXXXX385K&lt;/Income_TAX_PAN&gt;&lt;Passport_Number&gt;&lt;/Passport_Number&gt;&lt;Voter_ID_Number&gt;&lt;/Voter_ID_Number&gt;&lt;Date_of_birth&gt;********&lt;/Date_of_birth&gt;&lt;/CAIS_Holder_Details&gt;&lt;CAIS_Holder_Address_Details&gt;&lt;First_Line_Of_Address_non_normalized&gt;NEAR SINGH SABHA GURUDWARA MITHAPUR&lt;/First_Line_Of_Address_non_normalized&gt;&lt;Second_Line_Of_Address_non_normalized&gt;JALANDHAR 11 JALANDHAR   GARHA  PUNJAB&lt;/Second_Line_Of_Address_non_normalized&gt;&lt;Third_Line_Of_Address_non_normalized&gt;&lt;/Third_Line_Of_Address_non_normalized&gt;&lt;City_non_normalized&gt;&lt;/City_non_normalized&gt;&lt;Fifth_Line_Of_Address_non_normalized&gt;&lt;/Fifth_Line_Of_Address_non_normalized&gt;&lt;State_non_normalized&gt;03&lt;/State_non_normalized&gt;&lt;ZIP_Postal_Code_non_normalized&gt;144022&lt;/ZIP_Postal_Code_non_normalized&gt;&lt;CountryCode_non_normalized&gt;IB&lt;/CountryCode_non_normalized&gt;&lt;Address_indicator_non_normalized&gt;02&lt;/Address_indicator_non_normalized&gt;&lt;Residence_code_non_normalized&gt;&lt;/Residence_code_non_normalized&gt;&lt;/CAIS_Holder_Address_Details&gt;&lt;CAIS_Holder_Phone_Details&gt;&lt;Telephone_Number&gt;XXXXXX8942&lt;/Telephone_Number&gt;&lt;Telephone_Type&gt;02&lt;/Telephone_Type&gt;&lt;Telephone_Extension&gt;91&lt;/Telephone_Extension&gt;&lt;Mobile_Telephone_Number&gt;&lt;/Mobile_Telephone_Number&gt;&lt;FaxNumber&gt;&lt;/FaxNumber&gt;&lt;EMailId&gt;<EMAIL>&lt;/EMailId&gt;&lt;/CAIS_Holder_Phone_Details&gt;&lt;CAIS_Holder_Phone_Details&gt;&lt;Telephone_Number&gt;&lt;/Telephone_Number&gt;&lt;Telephone_Type&gt;01&lt;/Telephone_Type&gt;&lt;Telephone_Extension&gt;&lt;/Telephone_Extension&gt;&lt;Mobile_Telephone_Number&gt;XXXXX58942&lt;/Mobile_Telephone_Number&gt;&lt;FaxNumber&gt;&lt;/FaxNumber&gt;&lt;EMailId&gt;<EMAIL>&lt;/EMailId&gt;&lt;/CAIS_Holder_Phone_Details&gt;&lt;CAIS_Holder_ID_Details&gt;&lt;Income_TAX_PAN&gt;XXXXXX385K&lt;/Income_TAX_PAN&gt;&lt;PAN_Issue_Date&gt;&lt;/PAN_Issue_Date&gt;&lt;PAN_Expiration_Date&gt;&lt;/PAN_Expiration_Date&gt;&lt;Passport_Number&gt;&lt;/Passport_Number&gt;&lt;Passport_Issue_Date&gt;&lt;/Passport_Issue_Date&gt;&lt;Passport_Expiration_Date&gt;&lt;/Passport_Expiration_Date&gt;&lt;Voter_ID_Number&gt;&lt;/Voter_ID_Number&gt;&lt;Voter_ID_Issue_Date&gt;&lt;/Voter_ID_Issue_Date&gt;&lt;Voter_ID_Expiration_Date&gt;&lt;/Voter_ID_Expiration_Date&gt;&lt;Driver_License_Number&gt;&lt;/Driver_License_Number&gt;&lt;Driver_License_Issue_Date&gt;&lt;/Driver_License_Issue_Date&gt;&lt;Driver_License_Expiration_Date&gt;&lt;/Driver_License_Expiration_Date&gt;&lt;Ration_Card_Number&gt;&lt;/Ration_Card_Number&gt;&lt;Ration_Card_Issue_Date&gt;&lt;/Ration_Card_Issue_Date&gt;&lt;Ration_Card_Expiration_Date&gt;&lt;/Ration_Card_Expiration_Date&gt;&lt;Universal_ID_Number&gt;&lt;/Universal_ID_Number&gt;&lt;Universal_ID_Issue_Date&gt;&lt;/Universal_ID_Issue_Date&gt;&lt;Universal_ID_Expiration_Date&gt;&lt;/Universal_ID_Expiration_Date&gt;&lt;EMailId&gt;&lt;/EMailId&gt;&lt;/CAIS_Holder_ID_Details&gt;&lt;/CAIS_Account_DETAILS&gt;&lt;/CAIS_Account&gt;&lt;Match_result&gt;&lt;Exact_match&gt;Y&lt;/Exact_match&gt;&lt;/Match_result&gt;&lt;TotalCAPS_Summary&gt;&lt;TotalCAPSLast7Days&gt;0&lt;/TotalCAPSLast7Days&gt;&lt;TotalCAPSLast30Days&gt;0&lt;/TotalCAPSLast30Days&gt;&lt;TotalCAPSLast90Days&gt;0&lt;/TotalCAPSLast90Days&gt;&lt;TotalCAPSLast180Days&gt;0&lt;/TotalCAPSLast180Days&gt;&lt;/TotalCAPS_Summary&gt;&lt;CAPS&gt;&lt;CAPS_Summary&gt;&lt;CAPSLast7Days&gt;0&lt;/CAPSLast7Days&gt;&lt;CAPSLast30Days&gt;0&lt;/CAPSLast30Days&gt;&lt;CAPSLast90Days&gt;0&lt;/CAPSLast90Days&gt;&lt;CAPSLast180Days&gt;0&lt;/CAPSLast180Days&gt;&lt;/CAPS_Summary&gt;&lt;/CAPS&gt;&lt;NonCreditCAPS&gt;&lt;NonCreditCAPS_Summary&gt;&lt;NonCreditCAPSLast7Days&gt;0&lt;/NonCreditCAPSLast7Days&gt;&lt;NonCreditCAPSLast30Days&gt;0&lt;/NonCreditCAPSLast30Days&gt;&lt;NonCreditCAPSLast90Days&gt;0&lt;/NonCreditCAPSLast90Days&gt;&lt;NonCreditCAPSLast180Days&gt;0&lt;/NonCreditCAPSLast180Days&gt;&lt;/NonCreditCAPS_Summary&gt;&lt;/NonCreditCAPS&gt;&lt;SCORE&gt;&lt;BureauScore&gt;601&lt;/BureauScore&gt;&lt;BureauScoreConfidLevel&gt;&lt;/BureauScoreConfidLevel&gt;&lt;/SCORE&gt;&lt;/INProfileResponse&gt;");
            System.out.println(report);
            experianReport = xmlMapper.readValue(report, ExperianReport.class);
            System.out.println(experianReport);
        }catch (Exception e){
            System.out.println("Unable to parse report due to the following " + e.getMessage());
        }
    }

}