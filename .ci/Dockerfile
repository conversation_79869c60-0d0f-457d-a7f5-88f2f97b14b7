FROM jfrog.fkinternal.com/fk-base-images/openjdk/jdk:17.0.6-debian11.6-slim AS builder

RUN apt update && apt install -y maven

WORKDIR /src

RUN mkdir -p /root/.m2 && \
    echo '<settings><mirrors><mirror><id>Jfrog</id><name>Jfrog</name><url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url><mirrorOf>central</mirrorOf></mirror></mirrors></settings>' \
    > /root/.m2/settings.xml

RUN mvn clean -T 4 -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn -B org.jacoco:jacoco-maven-plugin:prepare-agent install

FROM jfrog.fkinternal.com/supermoney-engg/openjdk17-builder:5341313

ARG appId=sm-offer-service
ARG appDirectory=offer-service
WORKDIR /src

ARG user=pl
ARG group=supermoney
ARG uid=108
ARG gid=112

COPY --from=builder /src/${appDirectory}/target/${appDirectory}-*.jar /var/lib/${appId}/${appId}.jar
COPY ${appDirectory}/src/main/resources/fkagent/* /etc/fkagent/

RUN groupadd -g ${gid} ${group} && \
    useradd -u ${uid} -g ${gid} -m -s /bin/bash ${user} && \
    mkdir -p /var/lib/${appId} && chown -R ${user}:${group} /var/lib/${appId} && chmod -R u+rwx /var/lib/${appId} && \
    mkdir -p /var/lib/fkagent && chown -R ${user}:${group} /var/lib/fkagent && chmod -R u+rwx /var/lib/fkagent && \
    mkdir -p /var/log/sumo/ && chown -R ${user}:${group} /var/log/sumo/ && chmod -R a+rwx /var/log/sumo/ && \
    chown -R ${user}:${group} /entrypoint && chmod -R u+rwx /entrypoint && \
    sed -i "s/__PACKAGE__/${appId}/g" "/entrypoint"


RUN curl -L -X GET 'http://************/artifactory/maven_internal/com/flipkart/instrumentation/fk-agent/2.4-SM/fk-agent-2.4-SM-jar-with-dependencies.jar' --output /var/lib/fkagent/fk-agent.jar

RUN sed -i "s/__INCLUDE_FK_AGENT__/TRUE/" "/entrypoint"
RUN sed -i "s|__FK_AGENT_OPTS__|-javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml|" "/entrypoint"

ENTRYPOINT [ "/entrypoint" ]
CMD ["com.sumo.offer.service.OfferServiceApplication", "server", "/etc/config/config.yml"]

