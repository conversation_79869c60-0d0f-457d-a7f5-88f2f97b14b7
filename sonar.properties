# Required metadata
sonar.projectKey=supermoney:sm-offer-service
sonar.projectName=supermoney:sm-offer-service
sonar.projectVersion=1.1

# Comma-separated paths to directories with sources (required)
sonar.sources=./offer-service/src/main, ./offer-orchestrator/src/main, ./cohort-finder-v2/src/main, ./experiment-manager/src/main, ./lender-blackbox/src/main, ./offer-common/src/main, ./offer-evaluator-v2/src/main

sonar.verbose=true

# Tests
sonar.coverage.jacoco.xmlReportPaths = ./offer-orchestrator/target/site/jacoco-ut/jacoco.xml, ./offer-service/target/site/jacoco-ut/jacoco.xml, ./cohort-finder-v2/target/site/jacoco-ut/jacoco.xml, ./lender-blackbox/target/site/jacoco-ut/jacoco.xml, ./offer-common/target/site/jacoco-ut/jacoco.xml, ./offer-evaluator-v2/target/site/jacoco-ut/jacoco.xml
sonar.exclusions = **/generated/*,**/*.test.*, **/target/**, **/src/test/**, **/src/main/resources/**, **/src/test/resources/**, **/com/sumo/offer/orchestrator/module/**, **/offer-service/src/main/java/com/sumo/offer/service/OfferServiceModule.java

# Encoding of sources files
sonar.sourceEncoding = UTF-8