<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>sm-offer-service</artifactId>
        <groupId>com.sumo</groupId>
        <version>1.0.0-SM</version>
    </parent>

    <artifactId>offer-orchestrator</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-hibernate</artifactId>
            <version>${dropwizard.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.32</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>6.4.4.Final</version>
        </dependency>
        <dependency>
            <groupId>com.sumo</groupId>
            <artifactId>offer-common</artifactId>
            <version>1.0.0-SM</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-client</artifactId>
            <version>${pandora.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.cp.usercluster</groupId>
                    <artifactId>pathfinder</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fintech-redis-client</artifactId>
                    <groupId>com.flipkart.fintech</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pinaka-api</artifactId>
            <version>${pinaka.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>profile-service</artifactId>
            <version>${pinaka.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>fintech-logger</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.common.flogger</groupId>
                    <artifactId>flogger-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.common.flogger</groupId>
                    <artifactId>flogger-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.supermoney.commons.pubsub.client</groupId>
                    <artifactId>pub-sub-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>events-commons</groupId>
                    <artifactId>event-commons</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ezylang</groupId>
            <artifactId>EvalEx</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech.pinaka.library</groupId>
            <artifactId>pinaka-library</artifactId>
            <version>3.4.15-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo</groupId>
            <artifactId>offer-internal-models</artifactId>
            <version>1.0.0-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo</groupId>
            <artifactId>lender-blackbox</artifactId>
            <version>1.0.0-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo</groupId>
            <artifactId>application-service</artifactId>
            <version>1.0.0-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo</groupId>
            <artifactId>experiment-manager</artifactId>
            <version>1.0.0-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo</groupId>
            <artifactId>cohort-finder-v2</artifactId>
            <version>1.0.0-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo</groupId>
            <artifactId>offer-evaluator-v2</artifactId>
            <version>1.0.0-SM</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sumo.boomerang</groupId>
            <artifactId>boomerang-client</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.sumo.boomerang</groupId>
            <artifactId>boomerang-models</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.supermoney.cachy</groupId>
            <artifactId>cachy-core</artifactId>
            <version>1.0.14</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>