package com.sumo.offer.orchestrator.FilterService.ExpressionEvaluator.CustomFunctions;

import static org.mockito.Mockito.*;
import static org.testng.AssertJUnit.assertFalse;
import static org.testng.AssertJUnit.assertTrue;

import com.ezylang.evalex.Expression;
import com.ezylang.evalex.data.DataAccessorIfc;
import com.ezylang.evalex.data.EvaluationValue;
import com.ezylang.evalex.parser.Token;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class IsUserInLenderFailureStateTest {

  private IsUserInLenderFailureState isUserInLenderFailureState;

  @Mock private Expression expression;

  @Mock private DataAccessorIfc dataAccessor;

  @Before
  public void setUp() {
    try (AutoCloseable ignored = MockitoAnnotations.openMocks(this)) {
      isUserInLenderFailureState = new IsUserInLenderFailureState();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    when(expression.getDataAccessor()).thenReturn(dataAccessor);
  }

  @Test
  public void testEvaluateWithNoDiscardedApplications() {
    Token token = mock(Token.class);
    when(dataAccessor.getData("discardedApplications")).thenReturn(null);

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNoMatchingLender() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN_FIBE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderButDifferentState() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("REJECTED"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderAndFailureState() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMultipleApplications() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN_FIBE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app2 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app2.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app2.put("applicationState", EvaluationValue.stringValue("REJECTED"));
    app2.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app2.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app3 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app3.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app3.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app3.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app3.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));
    discardedApps.put("app2", EvaluationValue.structureValue(app2));
    discardedApps.put("app3", EvaluationValue.structureValue(app3));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNullApplicationState() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", null);
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test(expected = IllegalArgumentException.class)
  public void testEvaluateWithNullLender() {
    Token token = mock(Token.class);

    EvaluationValue[] parameterValues = {null};

    isUserInLenderFailureState.evaluate(expression, token, parameterValues);
  }

  @Test
  public void testEvaluateWithNullApplicationType() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", null);
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNullEvaluationValue() {
    Token token = mock(Token.class);

    // Create discarded applications data with a null entry
    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", null);

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithExceptionInLenderComparison() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // Invalid application type that will cause an exception
    app1.put("applicationType", EvaluationValue.stringValue("INVALID_TYPE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("INVALID_LENDER")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNonStructureEvaluationValue() {
    Token token = mock(Token.class);

    // Create discarded applications data with a non-structure value
    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.stringValue("Not a structure"));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderAndFailureStateWithinDefaultCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data with recent timestamp (within 7 days)
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderAndFailureStateOutsideDefaultCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data with old timestamp (8 days ago, outside 7-day default cooloff)
    long eightDaysAgo = System.currentTimeMillis() - (8L * 24 * 60 * 60 * 1000);
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(eightDaysAgo)));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(eightDaysAgo)));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMultipleApplicationsWithinCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN_FIBE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app2 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app2.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app2.put("applicationState", EvaluationValue.stringValue("REJECTED"));
    app2.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app2.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app3 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app3.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app3.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app3.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app3.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));
    discardedApps.put("app2", EvaluationValue.structureValue(app2));
    discardedApps.put("app3", EvaluationValue.structureValue(app3));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }
