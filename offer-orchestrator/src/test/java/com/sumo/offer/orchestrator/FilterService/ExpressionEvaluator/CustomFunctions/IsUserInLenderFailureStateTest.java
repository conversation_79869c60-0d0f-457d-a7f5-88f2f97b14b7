package com.sumo.offer.orchestrator.FilterService.ExpressionEvaluator.CustomFunctions;

import static org.mockito.Mockito.*;
import static org.testng.AssertJUnit.assertFalse;
import static org.testng.AssertJUnit.assertTrue;

import com.ezylang.evalex.Expression;
import com.ezylang.evalex.data.DataAccessorIfc;
import com.ezylang.evalex.data.EvaluationValue;
import com.ezylang.evalex.parser.Token;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class IsUserInLenderFailureStateTest {

  private IsUserInLenderFailureState isUserInLenderFailureState;

  @Mock private Expression expression;

  @Mock private DataAccessorIfc dataAccessor;

  @Before
  public void setUp() {
    try (AutoCloseable ignored = MockitoAnnotations.openMocks(this)) {
      isUserInLenderFailureState = new IsUserInLenderFailureState();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    when(expression.getDataAccessor()).thenReturn(dataAccessor);
  }

  @Test
  public void testEvaluateWithNoDiscardedApplications() {
    Token token = mock(Token.class);
    when(dataAccessor.getData("discardedApplications")).thenReturn(null);

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNoMatchingLender() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN_FIBE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderButDifferentState() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("REJECTED"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderAndFailureState() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMultipleApplications() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN_FIBE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app2 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app2.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app2.put("applicationState", EvaluationValue.stringValue("REJECTED"));
    app2.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app2.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app3 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app3.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app3.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app3.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app3.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));
    discardedApps.put("app2", EvaluationValue.structureValue(app2));
    discardedApps.put("app3", EvaluationValue.structureValue(app3));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNullApplicationState() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", null);
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test(expected = IllegalArgumentException.class)
  public void testEvaluateWithNullLender() {
    Token token = mock(Token.class);

    EvaluationValue[] parameterValues = {null};

    isUserInLenderFailureState.evaluate(expression, token, parameterValues);
  }

  @Test
  public void testEvaluateWithNullApplicationType() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", null);
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNullEvaluationValue() {
    Token token = mock(Token.class);

    // Create discarded applications data with a null entry
    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", null);

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithExceptionInLenderComparison() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // Invalid application type that will cause an exception
    app1.put("applicationType", EvaluationValue.stringValue("INVALID_TYPE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("INVALID_LENDER")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNonStructureEvaluationValue() {
    Token token = mock(Token.class);

    // Create discarded applications data with a non-structure value
    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.stringValue("Not a structure"));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderAndFailureStateWithinDefaultCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data with recent timestamp (within 7 days)
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMatchingLenderAndFailureStateOutsideDefaultCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data with old timestamp (8 days ago, outside 7-day default cooloff)
    long eightDaysAgo = System.currentTimeMillis() - (8L * 24 * 60 * 60 * 1000);
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(eightDaysAgo)));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(eightDaysAgo)));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMultipleApplicationsWithinCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN_FIBE"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app2 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app2.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app2.put("applicationState", EvaluationValue.stringValue("REJECTED"));
    app2.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app2.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> app3 = new HashMap<>();
    // For AXIS lender, the application type should be just "PERSONAL_LOAN" according to
    // ExpressionEvaluatorHelper
    app3.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app3.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app3.put(
        "createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app3.put(
        "updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));
    discardedApps.put("app2", EvaluationValue.structureValue(app2));
    discardedApps.put("app3", EvaluationValue.structureValue(app3));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithCustomCooloffPeriodWithinCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data with timestamp 5 days ago
    long fiveDaysAgo = System.currentTimeMillis() - (5L * 24 * 60 * 60 * 1000);
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(fiveDaysAgo)));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(fiveDaysAgo)));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    // Test with 10-day cooloff period (5 days ago is within 10-day cooloff)
    EvaluationValue[] parameterValues = {
        EvaluationValue.stringValue("AXIS"),
        EvaluationValue.numberValue(BigDecimal.valueOf(10))
    };

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithCustomCooloffPeriodOutsideCooloff() {
    Token token = mock(Token.class);

    // Create discarded applications data with timestamp 5 days ago
    long fiveDaysAgo = System.currentTimeMillis() - (5L * 24 * 60 * 60 * 1000);
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(fiveDaysAgo)));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(fiveDaysAgo)));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    // Test with 3-day cooloff period (5 days ago is outside 3-day cooloff)
    EvaluationValue[] parameterValues = {
        EvaluationValue.stringValue("AXIS"),
        EvaluationValue.numberValue(BigDecimal.valueOf(3))
    };

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithZeroCooloffPeriod() {
    Token token = mock(Token.class);

    // Create discarded applications data with recent timestamp
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    // Test with 0-day cooloff period (should immediately allow lender)
    EvaluationValue[] parameterValues = {
        EvaluationValue.stringValue("AXIS"),
        EvaluationValue.numberValue(BigDecimal.valueOf(0))
    };

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNegativeCooloffPeriod() {
    Token token = mock(Token.class);

    // Create discarded applications data with recent timestamp
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    // Test with negative cooloff period (should immediately allow lender)
    EvaluationValue[] parameterValues = {
        EvaluationValue.stringValue("AXIS"),
        EvaluationValue.numberValue(BigDecimal.valueOf(-5))
    };

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithRecentFailureAndOldFailure() {
    Token token = mock(Token.class);

    // Create multiple LENDER_FAILURE applications with different timestamps
    long tenDaysAgo = System.currentTimeMillis() - (10L * 24 * 60 * 60 * 1000);
    long threeDaysAgo = System.currentTimeMillis() - (3L * 24 * 60 * 60 * 1000);

    // Old LENDER_FAILURE application (outside cooloff)
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(tenDaysAgo)));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(tenDaysAgo)));

    // Recent LENDER_FAILURE application (within cooloff)
    Map<String, EvaluationValue> app2 = new HashMap<>();
    app2.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app2.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app2.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(threeDaysAgo)));
    app2.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(threeDaysAgo)));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));
    discardedApps.put("app2", EvaluationValue.structureValue(app2));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    // Test with 7-day cooloff period (recent failure should block lender)
    EvaluationValue[] parameterValues = {
        EvaluationValue.stringValue("AXIS"),
        EvaluationValue.numberValue(BigDecimal.valueOf(7))
    };

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertFalse(result.getBooleanValue());
  }

  // Exception handling tests
  @Test
  public void testEvaluateWithInvalidCooloffParameterType() {
    Token token = mock(Token.class);

    // Create discarded applications data
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    // Pass invalid cooloff parameter type (string instead of number)
    EvaluationValue[] parameterValues = {
        EvaluationValue.stringValue("AXIS"),
        EvaluationValue.stringValue("invalid_number")
    };

    // Should handle ClassCastException gracefully and default to allowing allocation
    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNullDataAccessor() {
    Token token = mock(Token.class);

    // Mock expression with null data accessor
    when(expression.getDataAccessor()).thenReturn(null);

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should return true when data accessor is null
    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithDataAccessorThrowingException() {
    Token token = mock(Token.class);

    // Mock data accessor to throw exception
    when(dataAccessor.getData("discardedApplications"))
        .thenThrow(new RuntimeException("Data access error"));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should handle exception gracefully and default to allowing allocation
    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithInvalidCreatedAtType() {
    Token token = mock(Token.class);

    // Create discarded applications data with invalid createdAt type
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.stringValue("invalid_date")); // String instead of number
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should handle ClassCastException in date parsing and default to allowing allocation
    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithExceptionInApplicationFiltering() {
    Token token = mock(Token.class);

    // Create a mock EvaluationValue that throws exception when getStructureValue() is called
    EvaluationValue mockApp = mock(EvaluationValue.class);
    when(mockApp.getStructureValue()).thenThrow(new RuntimeException("Structure access error"));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", mockApp);

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should handle exception in application filtering and default to allowing allocation
    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNullApplicationTypeException() {
    Token token = mock(Token.class);

    // Create application with null structure values to cause NullPointerException
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", null); // This will cause exception in String.valueOf()
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should handle exception in application type processing and default to allowing allocation
    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithExceptionInDateUtilsAddDays() {
    Token token = mock(Token.class);

    // Create application with extremely large timestamp that might cause overflow
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", EvaluationValue.numberValue(BigDecimal.valueOf(Long.MAX_VALUE)));
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {
        EvaluationValue.stringValue("AXIS"),
        EvaluationValue.numberValue(BigDecimal.valueOf(Integer.MAX_VALUE)) // Large cooloff period
    };

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should handle potential overflow/exception in date calculations and default to allowing allocation
    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithMalformedApplicationStructure() {
    Token token = mock(Token.class);

    // Create application with missing required fields
    Map<String, EvaluationValue> app1 = new HashMap<>();
    // Missing applicationType and applicationState - should cause issues in processing

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should handle malformed application structure gracefully
    assertTrue(result.getBooleanValue());
  }

  @Test
  public void testEvaluateWithNullCreatedAtValue() {
    Token token = mock(Token.class);

    // Create discarded applications data with null createdAt field
    Map<String, EvaluationValue> app1 = new HashMap<>();
    app1.put("applicationType", EvaluationValue.stringValue("PERSONAL_LOAN"));
    app1.put("applicationState", EvaluationValue.stringValue("LENDER_FAILURE"));
    app1.put("createdAt", null); // Explicitly set createdAt to null
    app1.put("updatedAt", EvaluationValue.numberValue(BigDecimal.valueOf(System.currentTimeMillis())));

    Map<String, EvaluationValue> discardedApps = new HashMap<>();
    discardedApps.put("app1", EvaluationValue.structureValue(app1));

    when(dataAccessor.getData("discardedApplications"))
        .thenReturn(EvaluationValue.structureValue(discardedApps));

    EvaluationValue[] parameterValues = {EvaluationValue.stringValue("AXIS")};

    EvaluationValue result = isUserInLenderFailureState.evaluate(expression, token, parameterValues);

    // Should return true (allow lender) when createdAt is null - cooloff check fails gracefully
    assertTrue(result.getBooleanValue());
  }
}
