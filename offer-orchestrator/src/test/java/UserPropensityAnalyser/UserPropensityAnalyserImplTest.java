package UserPropensityAnalyser;

import com.sumo.offer.common.bqIngestor.BQIngestionHelper;
import com.sumo.offer.internal.models.InternalModel.InternalModelResponse;
import com.sumo.offer.internal.models.InternalModel.InternalModelScores;
import com.sumo.offer.orchestrator.userPropensityAnalyser.InternalModelClient;
import com.sumo.offer.orchestrator.userPropensityAnalyser.UserPropensityAnalyserImpl;
import com.supermoney.schema.OfferService.UserInternalModelV1;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UserPropensityAnalyserImplTest {

    @Mock
    private InternalModelClient internalModelClient;

    @Mock
    private BQIngestionHelper bqIngestionHelper;

    @InjectMocks
    private UserPropensityAnalyserImpl analyser;

    private final String xmlReport = "<Report><Score>720</Score></Report>";
    private final String leadId = "lead123";
    private final String userId = "user456";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        analyser = new UserPropensityAnalyserImpl(internalModelClient, bqIngestionHelper);
    }

    @Test
    void testNullExperianReportReturnsNull() {
        InternalModelScores result = analyser.getUserPropensityScores(null, leadId, userId);
        assertNull(result);
        verifyNoInteractions(internalModelClient);
        verifyNoInteractions(bqIngestionHelper);
    }

    @Test
    void testNullScoreNotIngestsAndReturnsEmpty() {
        JSONObject mockFeatures = new JSONObject("{\"feature1\": 100}");
        when(internalModelClient.getFeatures(xmlReport, leadId)).thenReturn(mockFeatures);
        when(internalModelClient.getScores(mockFeatures, leadId)).thenReturn(null);
        InternalModelScores result = analyser.getUserPropensityScores(xmlReport, leadId, userId);
        assertNull(result);
        verify(bqIngestionHelper, never()).ingestInternalModelEvent(any(UserInternalModelV1.class));
    }

    @Test
    void testSuccessfulScoreReturned() {
        JSONObject mockFeatures = new JSONObject("{\"feature1\": 100}");
        InternalModelResponse internalModelResponseMock = new InternalModelResponse();
        internalModelResponseMock.setPropensityScore(0.87);
        internalModelResponseMock.setApprovalScore(0.1);
        when(internalModelClient.getFeatures(xmlReport, leadId)).thenReturn(mockFeatures);
        when(internalModelClient.getScores(mockFeatures, leadId)).thenReturn(internalModelResponseMock);
        InternalModelScores result = analyser.getUserPropensityScores(xmlReport, leadId, userId);
        assertNotNull(result);
        assertEquals(0.87, result.getPropensityScore());
        verify(bqIngestionHelper).ingestInternalModelEvent(any(UserInternalModelV1.class));
    }

}
