package com.sumo.offer.orchestrator.FilterService.ExpressionEvaluator.CustomFunctions;

import com.ezylang.evalex.Expression;
import com.ezylang.evalex.data.EvaluationValue;
import com.ezylang.evalex.functions.AbstractFunction;
import com.ezylang.evalex.functions.FunctionParameter;
import com.ezylang.evalex.parser.Token;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import org.apache.commons.lang.time.DateUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Collection;
import java.util.Date;

@FunctionParameter(name = "lender")
@FunctionParameter(name = "expiryInDays")
public class HasUserCompletedLenderCoolOff extends AbstractFunction {


    @Override
    public EvaluationValue evaluate(
            Expression expression, Token functionToken, EvaluationValue... parameterValues) {
        if(parameterValues[0] == null || parameterValues[1] == null) throw new IllegalArgumentException("Invalid inputs");
        String lender = (String) parameterValues[0].getValue();
        BigInteger expiryInDays = ((BigDecimal) parameterValues[1].getValue()).toBigInteger();
        // filter for lender applicationType and REJECTED application state
        if(expression.getDataAccessor().getData("discardedApplications") == null) return EvaluationValue.booleanValue(true);
        Collection<EvaluationValue> discardedApplications = expression.getDataAccessor().getData("discardedApplications").getStructureValue().values().stream()
                .filter(evaluationValue -> ExpressionEvaluatorHelper.isSameLender((String) evaluationValue.getStructureValue().get("applicationType").getValue(), Lender.valueOf(lender)))
                .filter(evaluationValue -> {
                    EvaluationValue applicationStateValue = evaluationValue.getStructureValue().get("applicationState");
                    return applicationStateValue != null && "REJECTED".equalsIgnoreCase((String) applicationStateValue.getValue());
                })
                .toList();
        // if there's any lender application which is within the configured expiry days return false
        for (EvaluationValue evaluationValue : discardedApplications) {
            Date createdAt = new Date(((BigDecimal) evaluationValue.getStructureValue().get("createdAt").getValue()).longValue());
            Date coolOffDate = DateUtils.addDays(createdAt, expiryInDays.intValue());
            Date currentDate = new Date();
            if(!coolOffDate.before(currentDate)) return EvaluationValue.booleanValue(false);
        }
        return EvaluationValue.booleanValue(true);
    }


}
