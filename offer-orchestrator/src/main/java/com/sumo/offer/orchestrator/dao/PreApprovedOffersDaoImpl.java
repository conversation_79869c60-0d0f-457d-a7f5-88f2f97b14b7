package com.sumo.offer.orchestrator.dao;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.sumo.offer.internal.models.LenderOfferEntity;
import com.sumo.offer.internal.models.entity.PreapprovedOffersEntity;
import com.sumo.offer.internal.models.entity.WhitelistEntity;
import com.sumo.offer.internal.models.entity.compositeKeys.PreapprovedUserId;
import io.dropwizard.hibernate.AbstractDAO;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 24/07/24
 */
@Slf4j
public class PreApprovedOffersDaoImpl extends AbstractDAO<PreapprovedOffersEntity> implements PreApprovedOffersDao {

    private final SessionFactory sessionFactory;

    private static final String WHITELIST = "whitelist";
    public static final String MERCHANT = "merchant";


    @Inject
    public PreApprovedOffersDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
        this.sessionFactory = sessionFactory;
    }


    @Override
    public void save(PreapprovedOffersEntity preapprovedOffersEntity) {
        persist(preapprovedOffersEntity);
    }

    @Override
    public PreapprovedOffersEntity getPreapprovedOffersBySmUserIdAndOfferId(String smUserId, String offerId){
        return get(new PreapprovedUserId(smUserId, offerId));
    }

    @Override
    public List<LenderOfferEntity> getOffersForUserProfile(String smUserId, List<WhitelistEntity> activeWhitelists) {
        Session currentSession = sessionFactory.getCurrentSession();
        CriteriaBuilder cb = currentSession.getCriteriaBuilder();
        CriteriaQuery<PreapprovedOffersEntity> cq = cb.createQuery(PreapprovedOffersEntity.class);

        Root<PreapprovedOffersEntity> paRoot = cq.from(PreapprovedOffersEntity.class);

        List<Long> whitelistIds = activeWhitelists.stream().map(WhitelistEntity::getWhitelistId).toList();

        // where clause
        Predicate smUserIdPredicate = cb.equal(paRoot.get("smUserId"), smUserId);
        Predicate smUserEnabledPredicate =  cb.equal(paRoot.get("enabled"), true);
        Predicate whitelistPredicate = paRoot.get("whitelistId").in(whitelistIds);

        Predicate finalPredicate = cb.and(smUserIdPredicate, smUserEnabledPredicate, whitelistPredicate);

        cq.where(finalPredicate);

        List<PreapprovedOffersEntity> resultSet =
                currentSession.createQuery(cq).getResultList();
        if(resultSet.isEmpty()) {
            return new ArrayList<>();
        } else {
            List<LenderOfferEntity> lenderOffers = new ArrayList<>();
            for(PreapprovedOffersEntity preapprovedOffersEntity : resultSet){
                Optional<WhitelistEntity> whitelistEntity = activeWhitelists.stream().filter(entity -> Objects.equals(entity.getWhitelistId(), preapprovedOffersEntity.getWhitelistId())).findFirst();
                whitelistEntity.ifPresent(entity -> lenderOffers.add(preapprovedOffersEntity.mapToLenderOfferEntity(entity)));
            }
            return lenderOffers;
        }
    }



}
