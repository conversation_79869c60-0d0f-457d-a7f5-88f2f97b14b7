package com.sumo.offer.orchestrator.FilterService.ExpressionEvaluator.CustomFunctions;

import com.ezylang.evalex.Expression;
import com.ezylang.evalex.data.EvaluationValue;
import com.ezylang.evalex.functions.AbstractFunction;
import com.ezylang.evalex.functions.FunctionParameter;
import com.ezylang.evalex.parser.Token;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Collection;
import java.util.Date;

@Slf4j
@FunctionParameter(name = "lender")
@FunctionParameter(name = "cooloffInDays")
public class IsUserInLenderFailureState extends AbstractFunction {

  private static final String LENDER_FAILURE_STATE = "LENDER_FAILURE";
  private static final String DISCARDED_APPLICATIONS = "discardedApplications";
  private static final int DEFAULT_COOLOFF_DAYS = 7;

  @Override
  public EvaluationValue evaluate(
      Expression expression, Token functionToken, EvaluationValue... parameterValues) {

    if (parameterValues.length == 0 || parameterValues[0] == null) {
      throw new IllegalArgumentException("Invalid inputs");
    }

    String lender = String.valueOf(parameterValues[0].getValue());

    // Get cooloff period - use provided value or default to 7 days
    int cooloffInDays = DEFAULT_COOLOFF_DAYS;
    if (parameterValues.length > 1 && parameterValues[1] != null) {
      cooloffInDays = ((BigDecimal) parameterValues[1].getValue()).toBigInteger().intValue();
    }

    if (!hasDiscardedApplicationsData(expression)) {
      return EvaluationValue.booleanValue(true);
    }

    boolean hasActiveFailureStateApplications = hasActiveFailureStateApplications(expression, lender, cooloffInDays);

    // Return false if there are active failure state applications (still in cooloff period)
    return EvaluationValue.booleanValue(!hasActiveFailureStateApplications);
  }

  private boolean hasDiscardedApplicationsData(Expression expression) {
    return expression.getDataAccessor() != null
        && expression.getDataAccessor().getData(DISCARDED_APPLICATIONS) != null
        && expression.getDataAccessor().getData(DISCARDED_APPLICATIONS).getStructureValue() != null;
  }

  private boolean hasActiveFailureStateApplications(Expression expression, String lender, int cooloffInDays) {
    try {
      Collection<EvaluationValue> failureStateApplications = expression
          .getDataAccessor()
          .getData(DISCARDED_APPLICATIONS)
          .getStructureValue()
          .values()
          .stream()
          .filter(evaluationValue -> isLenderApplicationInFailureState(evaluationValue, lender))
          .toList();

      // Check if any failure state applications are still within cooloff period
      for (EvaluationValue application : failureStateApplications) {
        if (isWithinCooloffPeriod(application, cooloffInDays)) {
          return true; // Found an active failure state application
        }
      }
      return false; // No active failure state applications
    } catch (Exception e) {
      log.error(
          "Error finding active failure state applications for lender {}: {}", lender, e.getMessage(), e);
      return false;
    }
  }

  private boolean isLenderApplicationInFailureState(EvaluationValue application, String lender) {
    if (application == null || !application.isStructureValue()) return false;

    EvaluationValue appTypeValue = application.getStructureValue().get("applicationType");
    EvaluationValue appStateValue = application.getStructureValue().get("applicationState");

    if (appTypeValue == null || appStateValue == null) return false;

    String appType = String.valueOf(appTypeValue.getValue());
    String appState = String.valueOf(appStateValue.getValue());

    Lender lenderEnum;
    try {
      lenderEnum = Lender.valueOf(lender);
    } catch (IllegalArgumentException e) {
      log.warn("Invalid lender value: {}", lender);
      return false;
    }

    return ExpressionEvaluatorHelper.isSameLender(appType, lenderEnum)
        && LENDER_FAILURE_STATE.equalsIgnoreCase(appState);
  }

  private boolean isWithinCooloffPeriod(EvaluationValue application, int cooloffInDays) {
    try {
      // If cooloff period is 0 or negative, always allow the lender
      if (cooloffInDays <= 0) {
        return false;
      }

      EvaluationValue createdAtValue = application.getStructureValue().get("createdAt");
      if (createdAtValue == null) {
        log.warn("createdAt field is missing for application");
        return false;
      }

      Date createdAt = new Date(((BigDecimal) createdAtValue.getValue()).longValue());
      Date coolOffDate = DateUtils.addDays(createdAt, cooloffInDays);
      Date currentDate = new Date();

      // Return true if cooloff period has not expired (still within cooloff)
      return !coolOffDate.before(currentDate);
    } catch (Exception e) {
      log.error("Error checking cooloff period for application: {}", e.getMessage(), e);
      return false;
    }
  }
}
