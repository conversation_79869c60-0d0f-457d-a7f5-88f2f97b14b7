package com.sumo.offer.orchestrator.FilterService.ExpressionEvaluator.CustomFunctions;

import com.ezylang.evalex.Expression;
import com.ezylang.evalex.data.EvaluationValue;
import com.ezylang.evalex.functions.AbstractFunction;
import com.ezylang.evalex.functions.FunctionParameter;
import com.ezylang.evalex.parser.Token;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Collection;
import java.util.Date;

@Slf4j
@FunctionParameter(name = "lender")
public class IsUserInLenderFailureState extends AbstractFunction {

  private static final String LENDER_FAILURE_STATE = "LENDER_FAILURE";
  private static final String DISCARDED_APPLICATIONS = "discardedApplications";

  @Override
  public EvaluationValue evaluate(
      Expression expression, Token functionToken, EvaluationValue... parameterValues) {

    if (parameterValues.length == 0 || parameterValues[0] == null) {
      throw new IllegalArgumentException("Invalid inputs");
    }

    String lender = String.valueOf(parameterValues[0].getValue());

    if (!hasDiscardedApplicationsData(expression)) {
      return EvaluationValue.booleanValue(true);
    }

    boolean hasFailureStateApplications = findFailureStateApplications(expression, lender);

    // Return true if no applications are in failure state
    return EvaluationValue.booleanValue(!hasFailureStateApplications);
  }

  private boolean hasDiscardedApplicationsData(Expression expression) {
    return expression.getDataAccessor() != null
        && expression.getDataAccessor().getData(DISCARDED_APPLICATIONS) != null
        && expression.getDataAccessor().getData(DISCARDED_APPLICATIONS).getStructureValue() != null;
  }

  private boolean findFailureStateApplications(Expression expression, String lender) {
    try {
      return expression
          .getDataAccessor()
          .getData(DISCARDED_APPLICATIONS)
          .getStructureValue()
          .values()
          .stream()
          .anyMatch(evaluationValue -> isLenderApplicationInFailureState(evaluationValue, lender));
    } catch (Exception e) {
      log.error(
          "Error finding failure state applications for lender {}: {}", lender, e.getMessage(), e);
      return false;
    }
  }

  private boolean isLenderApplicationInFailureState(EvaluationValue application, String lender) {
    if (application == null || !application.isStructureValue()) return false;

    EvaluationValue appTypeValue = application.getStructureValue().get("applicationType");
    EvaluationValue appStateValue = application.getStructureValue().get("applicationState");

    if (appTypeValue == null || appStateValue == null) return false;

    String appType = String.valueOf(appTypeValue.getValue());
    String appState = String.valueOf(appStateValue.getValue());

    Lender lenderEnum;
    try {
      lenderEnum = Lender.valueOf(lender);
    } catch (IllegalArgumentException e) {
      log.warn("Invalid lender value: {}", lender);
      return false;
    }

    return ExpressionEvaluatorHelper.isSameLender(appType, lenderEnum)
        && LENDER_FAILURE_STATE.equalsIgnoreCase(appState);
  }
}
